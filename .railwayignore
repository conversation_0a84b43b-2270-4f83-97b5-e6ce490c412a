# Railway ignore file for backend deployment
# Exclude frontend files and development artifacts

# Frontend files
node_modules/
dist/
src/
public/
*.html
*.ts
*.tsx
*.js
*.jsx
*.css
*.scss
*.sass
*.less
*.vue
*.svelte

# Frontend config files
package.json
package-lock.json
yarn.lock
pnpm-lock.yaml
vite.config.*
tsconfig.*
tailwind.config.*
postcss.config.*
eslint.config.*
.eslintrc.*
.prettierrc.*
components.json

# Development files
.git/
.gitignore
.env.local
.env.development
.env.test
*.log
*.pid
*.seed
*.pid.lock

# Documentation
README.md
docs/
*.md

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS files
.DS_Store
Thumbs.db

# Temporary files
tmp/
temp/
*.tmp
*.temp

# Test files
test/
tests/
__tests__/
*.test.*
*.spec.*
coverage/

# Build artifacts (keep only backend)
build/
out/

# Keep only backend related files
!backend/
!railway.toml
!.env.production
!deploy.sh
