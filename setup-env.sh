#!/bin/bash

# 🔧 VideoChat AI - Environment Setup Script
# This script helps you set up environment variables for deployment

echo "🔧 VideoChat AI - Environment Setup"
echo "==================================="
echo ""

# Function to prompt for input with default value
prompt_with_default() {
    local prompt="$1"
    local default="$2"
    local var_name="$3"
    
    if [ -n "$default" ]; then
        read -p "$prompt [$default]: " input
        if [ -z "$input" ]; then
            input="$default"
        fi
    else
        read -p "$prompt: " input
        while [ -z "$input" ]; do
            echo "This field is required!"
            read -p "$prompt: " input
        done
    fi
    
    eval "$var_name='$input'"
}

echo "📋 Let's set up your environment variables for production deployment."
echo ""

# Required variables
echo "🔑 Required Configuration:"
echo ""

prompt_with_default "Enter your Gemini API key (get from https://aistudio.google.com/)" "" "GEMINI_API_KEY"
prompt_with_default "Enter your app domain (e.g., https://myapp.railway.app)" "" "APP_DOMAIN"

echo ""
echo "⚙️ Optional Configuration (press Enter for defaults):"
echo ""

prompt_with_default "Maximum video size in MB" "500" "MAX_VIDEO_SIZE"
prompt_with_default "Frame extraction interval (seconds)" "5" "FRAME_INTERVAL"
prompt_with_default "Debug mode (true/false)" "false" "DEBUG_MODE"

# Generate environment file
echo ""
echo "📝 Generating environment configuration..."

cat > .env.production << EOF
# 🚀 VideoChat AI - Production Environment
# Generated on $(date)

# Core Configuration
GEMINI_API_KEY=$GEMINI_API_KEY
DATABASE_URL=\${{Postgres.DATABASE_URL}}
HOST=0.0.0.0
PORT=\${{PORT}}
DEBUG=$DEBUG_MODE

# CORS Configuration
CORS_ORIGINS=$APP_DOMAIN

# Performance Settings
MAX_VIDEO_SIZE_MB=$MAX_VIDEO_SIZE
FRAME_EXTRACTION_INTERVAL=$FRAME_INTERVAL
MAX_VIDEO_DURATION_SECONDS=3600
MAX_FRAMES_PER_VIDEO=300

# Optional: Redis for better performance
REDIS_URL=redis://localhost:6379/0
CACHE_TTL=3600

# Optional: Advanced video search
AUTO_INDEX_UPLOADS=true
FRAME_SAMPLING_INTERVAL=5
TARGET_SEARCH_LATENCY_MS=20
ENABLE_HALF_PRECISION=true

# File Storage
UPLOAD_DIR=uploads
INDICES_DIR=indices
THUMBNAILS_DIR=thumbnails
EOF

echo "✅ Environment file created: .env.production"
echo ""

# Create Railway-specific environment variables
echo "🚂 Railway Environment Variables:"
echo "Copy these to your Railway dashboard:"
echo ""
echo "GEMINI_API_KEY=$GEMINI_API_KEY"
echo "DATABASE_URL=\${{Postgres.DATABASE_URL}}"
echo "HOST=0.0.0.0"
echo "PORT=\${{PORT}}"
echo "CORS_ORIGINS=$APP_DOMAIN"
echo "DEBUG=$DEBUG_MODE"
echo "MAX_VIDEO_SIZE_MB=$MAX_VIDEO_SIZE"
echo "FRAME_EXTRACTION_INTERVAL=$FRAME_INTERVAL"
echo ""

# Create Vercel-specific environment variables
echo "⚡ Vercel Environment Variables (for frontend):"
echo "Copy these to your Vercel dashboard:"
echo ""
echo "VITE_API_URL=$APP_DOMAIN"
echo ""

# Create Docker environment file
cat > .env << EOF
# 🐳 Docker Development Environment
GEMINI_API_KEY=$GEMINI_API_KEY
DATABASE_URL=**************************************/videochat
REDIS_URL=redis://redis:6379/0
HOST=0.0.0.0
PORT=8002
DEBUG=$DEBUG_MODE
CORS_ORIGINS=http://localhost:3000,http://localhost:8080
MAX_VIDEO_SIZE_MB=$MAX_VIDEO_SIZE
FRAME_EXTRACTION_INTERVAL=$FRAME_INTERVAL
EOF

echo "✅ Docker environment file created: .env"
echo ""

# Create deployment checklist
echo "📋 Next Steps:"
echo ""
echo "1. 🚂 For Railway deployment:"
echo "   - Go to https://railway.app"
echo "   - Connect your GitHub repository"
echo "   - Add PostgreSQL service"
echo "   - Copy the Railway environment variables above"
echo "   - Deploy!"
echo ""
echo "2. ⚡ For Vercel + Railway:"
echo "   - Deploy backend on Railway (step 1)"
echo "   - Deploy frontend on Vercel with the Vercel environment variables"
echo ""
echo "3. 🐳 For Docker deployment:"
echo "   - Run: docker-compose up -d"
echo "   - Access at http://localhost:3000"
echo ""
echo "📚 Documentation:"
echo "- Complete guide: PRODUCTION_DEPLOYMENT.md"
echo "- Deployment checklist: DEPLOYMENT_CHECKLIST.md"
echo "- Summary: DEPLOYMENT_SUMMARY.md"
echo ""
echo "🎉 Your environment is configured and ready for deployment!"
