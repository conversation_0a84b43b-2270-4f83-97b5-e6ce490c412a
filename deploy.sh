#!/bin/bash

# 🚀 VideoChat AI - Production Deployment Script
# This script helps you deploy your application to various platforms

set -e  # Exit on any error

echo "🚀 VideoChat AI - Production Deployment"
echo "========================================"
echo ""

# Check if we're in the right directory
if [ ! -f "package.json" ] || [ ! -d "backend" ]; then
    echo "❌ Error: Please run this script from the project root directory"
    exit 1
fi

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check required tools
echo "🔍 Checking required tools..."
if ! command_exists git; then
    echo "❌ Git is required but not installed"
    exit 1
fi

if ! command_exists node; then
    echo "❌ Node.js is required but not installed"
    exit 1
fi

if ! command_exists python3; then
    echo "❌ Python 3 is required but not installed"
    exit 1
fi

echo "✅ All required tools are available"
echo ""

# Get deployment target
echo "🎯 Select deployment target:"
echo "1) Railway + Vercel (Recommended for MVP)"
echo "2) Docker + VPS"
echo "3) AWS/GCP (Manual setup)"
echo "4) Local production build"
echo ""
read -p "Enter your choice (1-4): " choice

case $choice in
    1)
        echo ""
        echo "🚂 Railway + Vercel Deployment"
        echo "=============================="
        
        # Check if .env.production exists
        if [ ! -f ".env.production" ]; then
            echo "❌ .env.production file not found"
            echo "Please create it with your production environment variables"
            exit 1
        fi
        
        # Build frontend
        echo "📦 Building frontend..."
        npm install
        npm run build
        
        echo ""
        echo "✅ Frontend built successfully!"
        echo ""
        echo "📋 Next steps:"
        echo ""
        echo "1. 🚂 Deploy Backend on Railway:"
        echo "   - Go to https://railway.app"
        echo "   - Connect this GitHub repository"
        echo "   - Add PostgreSQL service"
        echo "   - Add Redis service (optional)"
        echo "   - Set these environment variables:"
        echo ""
        echo "   GEMINI_API_KEY=your_actual_key"
        echo "   DATABASE_URL=\${{Postgres.DATABASE_URL}}"
        echo "   REDIS_URL=\${{Redis.REDIS_URL}}"
        echo "   HOST=0.0.0.0"
        echo "   PORT=\${{PORT}}"
        echo "   DEBUG=false"
        echo "   CORS_ORIGINS=https://your-app.vercel.app"
        echo ""
        echo "2. ⚡ Deploy Frontend on Vercel:"
        echo "   - Go to https://vercel.com"
        echo "   - Import from GitHub"
        echo "   - Set build command: npm run build"
        echo "   - Set environment variable:"
        echo "   VITE_API_URL=https://your-backend.railway.app"
        echo ""
        ;;
        
    2)
        echo ""
        echo "🐳 Docker + VPS Deployment"
        echo "=========================="
        
        # Check if docker-compose.yml exists
        if [ ! -f "docker-compose.yml" ]; then
            echo "❌ docker-compose.yml file not found"
            exit 1
        fi
        
        # Check if Docker is installed
        if ! command_exists docker; then
            echo "❌ Docker is required but not installed"
            echo "Please install Docker: https://docs.docker.com/get-docker/"
            exit 1
        fi
        
        if ! command_exists docker-compose; then
            echo "❌ Docker Compose is required but not installed"
            exit 1
        fi
        
        echo "🔧 Setting up environment..."
        if [ ! -f ".env" ]; then
            cp .env.production .env
            echo "⚠️  Please update .env file with your actual values"
        fi
        
        echo "🏗️  Building and starting containers..."
        docker-compose down
        docker-compose build
        docker-compose up -d
        
        echo ""
        echo "✅ Application deployed successfully!"
        echo "🌐 Frontend: http://localhost:3000"
        echo "🔧 Backend: http://localhost:8002"
        echo "📊 Health check: http://localhost:8002/health"
        echo ""
        echo "📋 Next steps for VPS deployment:"
        echo "1. Set up nginx reverse proxy"
        echo "2. Configure SSL with Let's Encrypt"
        echo "3. Set up monitoring and backups"
        ;;
        
    3)
        echo ""
        echo "☁️  AWS/GCP Deployment"
        echo "====================="
        echo ""
        echo "For AWS/GCP deployment, please follow these steps:"
        echo ""
        echo "🏗️  Infrastructure Setup:"
        echo "1. Set up container registry (ECR/GCR)"
        echo "2. Create managed database (RDS/Cloud SQL)"
        echo "3. Set up Redis (ElastiCache/Memorystore)"
        echo "4. Configure load balancer"
        echo "5. Set up CDN (CloudFront/Cloud CDN)"
        echo ""
        echo "🚀 Application Deployment:"
        echo "1. Build and push Docker images"
        echo "2. Deploy to container service (ECS/Cloud Run)"
        echo "3. Configure environment variables"
        echo "4. Set up monitoring and logging"
        echo ""
        echo "📚 Detailed guides available in documentation"
        ;;
        
    4)
        echo ""
        echo "🏠 Local Production Build"
        echo "========================"
        
        echo "📦 Installing dependencies..."
        npm install
        cd backend && pip3 install -r requirements.txt && cd ..
        
        echo "🏗️  Building frontend..."
        npm run build
        
        echo "🔧 Setting up environment..."
        if [ ! -f "backend/.env" ]; then
            cp backend/.env.example backend/.env
            echo "⚠️  Please update backend/.env file with your actual values"
        fi
        
        echo ""
        echo "✅ Production build completed!"
        echo ""
        echo "🚀 To start the application:"
        echo "1. Start backend: cd backend && python3 main.py"
        echo "2. Serve frontend: npm run preview"
        echo ""
        ;;
        
    *)
        echo "❌ Invalid choice. Please run the script again."
        exit 1
        ;;
esac

echo ""
echo "🎉 Deployment preparation completed!"
echo ""
echo "📚 Additional Resources:"
echo "- Documentation: README.md"
echo "- Environment setup: .env.production"
echo "- Docker setup: docker-compose.yml"
echo ""
echo "💡 Need help? Check the documentation or create an issue on GitHub"
