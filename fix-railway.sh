#!/bin/bash

echo "🔧 Fixing Railway Deployment for VideoChat AI"
echo "=============================================="

# Create backend-only deployment directory
echo "📁 Creating backend deployment directory..."
mkdir -p ../videochat-backend-deploy
cd ../videochat-backend-deploy

# Copy backend files
echo "📋 Copying backend files..."
cp -r ../videoSearch/backend/* .
cp ../videoSearch/backend/.env . 2>/dev/null || echo "No .env file found, will create one"

# Create optimized Dockerfile for Railway
echo "🐳 Creating optimized Dockerfile..."
cat > Dockerfile << 'EOF'
FROM python:3.11-slim

# Install system dependencies
RUN apt-get update && apt-get install -y \
    ffmpeg \
    git \
    build-essential \
    curl \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app

# Copy requirements and install dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir --upgrade pip
RUN pip install --no-cache-dir torch torchvision --index-url https://download.pytorch.org/whl/cpu
RUN pip install --no-cache-dir git+https://github.com/openai/CLIP.git
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Create directories
RUN mkdir -p uploads indices thumbnails

# Set environment variables
ENV PYTHONPATH=/app
ENV HOST=0.0.0.0
ENV PORT=8002

EXPOSE 8002

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8002/health || exit 1

CMD ["python", "main.py"]
EOF

# Create .env file if it doesn't exist
if [ ! -f ".env" ]; then
    echo "📝 Creating .env file..."
    cat > .env << 'EOF'
# Backend Configuration
DATABASE_URL=sqlite:///./videochat.db
FRAME_EXTRACTION_INTERVAL=5

# Gemini API Configuration
GEMINI_API_KEY=AIzaSyBGz0jiD_oXZMFUNqYpst4IGs-71lNdhRw

# Production Settings
DEBUG=false
HOST=0.0.0.0
PORT=8002
CORS_ORIGINS=http://localhost:3000

# Video Processing
MAX_VIDEO_SIZE_MB=500
MAX_VIDEO_DURATION_SECONDS=3600
MAX_FRAMES_PER_VIDEO=300

# Vector Database
CHROMA_PERSIST_DIRECTORY=./chroma_db

# Redis Cache
REDIS_URL=redis://localhost:6379/0
CACHE_TTL=3600

# Gemini Settings
GEMINI_MODEL=gemini-2.5-flash-preview-05-20
GEMINI_VIDEO_RESOLUTION=default
GEMINI_MAX_VIDEO_FRAMES=256
EOF
fi

# Initialize git repository
echo "🔄 Initializing git repository..."
git init
git add .
git commit -m "Backend deployment for Railway"

echo ""
echo "✅ Backend deployment directory created!"
echo ""
echo "📋 Next steps:"
echo "1. Create a new GitHub repository called 'videochat-backend'"
echo "2. Run these commands:"
echo "   cd ../videochat-backend-deploy"
echo "   git remote add origin https://github.com/yourusername/videochat-backend.git"
echo "   git push -u origin main"
echo "3. Deploy this new repository to Railway"
echo ""
echo "🚀 Railway Environment Variables to set:"
echo "GEMINI_API_KEY=AIzaSyBGz0jiD_oXZMFUNqYpst4IGs-71lNdhRw"
echo "DATABASE_URL=\${{Postgres.DATABASE_URL}}"
echo "HOST=0.0.0.0"
echo "PORT=\${{PORT}}"
echo "DEBUG=false"
echo "CORS_ORIGINS=https://your-frontend.vercel.app"
echo ""
echo "Directory created at: $(pwd)"
