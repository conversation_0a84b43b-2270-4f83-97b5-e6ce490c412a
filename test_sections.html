<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Video Sections</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #1a1a1a;
            color: white;
        }
        .section {
            background: #2a2a2a;
            border: 1px solid #444;
            border-radius: 8px;
            padding: 16px;
            margin: 16px 0;
            cursor: pointer;
        }
        .section:hover {
            background: #333;
        }
        .section-title {
            font-weight: bold;
            font-size: 18px;
            margin-bottom: 8px;
        }
        .section-time {
            color: #888;
            font-size: 14px;
            margin-bottom: 8px;
        }
        .section-description {
            margin-bottom: 12px;
            line-height: 1.4;
        }
        .section-topics {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }
        .topic {
            background: #444;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
        }
        .button {
            background: white;
            color: black;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: bold;
            margin: 8px;
        }
        .button:hover {
            background: #ddd;
        }
        .button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        .status {
            padding: 12px;
            margin: 12px 0;
            border-radius: 8px;
            background: #333;
        }
        .error {
            background: #4a1a1a;
            color: #ff6b6b;
        }
        .success {
            background: #1a4a1a;
            color: #51cf66;
        }
    </style>
</head>
<body>
    <h1>Video Sections Test</h1>
    
    <div>
        <button class="button" onclick="testVideo77()">Test Video 77 (Has Sections)</button>
        <button class="button" onclick="testVideo78()">Test Video 78 (No Sections)</button>
        <button class="button" onclick="generateSections78()" id="generateBtn">Generate Sections for Video 78</button>
    </div>
    
    <div id="status" class="status" style="display: none;"></div>
    <div id="sections"></div>

    <script>
        function showStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
            statusDiv.style.display = 'block';
        }

        function hideStatus() {
            document.getElementById('status').style.display = 'none';
        }

        async function testVideo77() {
            showStatus('Loading sections for Video 77...');
            try {
                const response = await fetch('http://localhost:8002/api/v1/video/77/sections');
                const data = await response.json();
                console.log('Video 77 sections:', data);
                displaySections(data.sections, 77);
                showStatus(`Loaded ${data.sections.length} sections for Video 77`, 'success');
            } catch (error) {
                console.error('Error:', error);
                showStatus('Error loading Video 77 sections: ' + error.message, 'error');
            }
        }

        async function testVideo78() {
            showStatus('Loading sections for Video 78...');
            try {
                const response = await fetch('http://localhost:8002/api/v1/video/78/sections');
                const data = await response.json();
                console.log('Video 78 sections:', data);
                displaySections(data.sections, 78);
                if (data.sections.length === 0) {
                    showStatus('Video 78 has no sections. Use "Generate Sections" button.', 'info');
                } else {
                    showStatus(`Loaded ${data.sections.length} sections for Video 78`, 'success');
                }
            } catch (error) {
                console.error('Error:', error);
                showStatus('Error loading Video 78 sections: ' + error.message, 'error');
            }
        }

        async function generateSections78() {
            const btn = document.getElementById('generateBtn');
            btn.disabled = true;
            btn.textContent = 'Generating...';
            showStatus('Generating sections for Video 78... This may take 30-60 seconds.');
            
            try {
                const response = await fetch('http://localhost:8002/api/v1/video/78/sections/generate', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                });
                const data = await response.json();
                console.log('Generated sections:', data);
                
                if (data.sections && data.sections.length > 0) {
                    displaySections(data.sections, 78);
                    showStatus(`Successfully generated ${data.sections.length} sections for Video 78!`, 'success');
                } else {
                    showStatus('No sections were generated', 'error');
                }
            } catch (error) {
                console.error('Error:', error);
                showStatus('Error generating sections: ' + error.message, 'error');
            } finally {
                btn.disabled = false;
                btn.textContent = 'Generate Sections for Video 78';
            }
        }

        function displaySections(sections, videoId) {
            const sectionsDiv = document.getElementById('sections');
            
            if (!sections || sections.length === 0) {
                sectionsDiv.innerHTML = `<p>No sections available for Video ${videoId}</p>`;
                return;
            }

            let html = `<h2>Video ${videoId} Sections (${sections.length} total)</h2>`;
            
            sections.forEach((section, index) => {
                html += `
                    <div class="section" onclick="jumpToTime('${section.start_time}')">
                        <div class="section-title">${index + 1}. ${section.title}</div>
                        <div class="section-time">${section.start_time} - ${section.end_time}</div>
                        <div class="section-description">${section.description}</div>
                        <div class="section-topics">
                            ${section.key_topics.map(topic => `<span class="topic">#${topic}</span>`).join('')}
                        </div>
                    </div>
                `;
            });
            
            sectionsDiv.innerHTML = html;
        }

        function jumpToTime(timeStr) {
            alert(`Would jump to time: ${timeStr}`);
        }

        // Test on page load
        window.onload = function() {
            showStatus('Ready to test video sections. Click a button above to start.');
        };
    </script>
</body>
</html>
