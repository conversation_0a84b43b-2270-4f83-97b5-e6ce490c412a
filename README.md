# VidChat - Multimodal Video Analysis System

A powerful multimodal video analysis system that enables intelligent chat with YouTube videos, generates timestamped sections, and provides advanced visual content search using <PERSON>'s video understanding capabilities.

## 🚀 Quick Deploy

Deploy to production in one click:

[![Deploy on Railway](https://railway.app/button.svg)](https://railway.app/new/template/videochat-ai)

**Or choose your deployment method:**
- 🚂 **Railway** (Recommended): [Complete Guide](PRODUCTION_DEPLOYMENT.md#option-1-railway-recommended---easiest)
- ⚡ **Vercel + Railway**: [Setup Guide](PRODUCTION_DEPLOYMENT.md#option-2-vercel--railway-best-performance)
- 🐳 **Docker**: [Docker Guide](PRODUCTION_DEPLOYMENT.md#option-3-docker-compose-vpslocal)

**Ready in 15 minutes!** See [PRODUCTION_DEPLOYMENT.md](PRODUCTION_DEPLOYMENT.md) for detailed instructions.

## Key Features

### 1. **RAG-Based Video Chat**
- Upload videos or provide YouTube links
- Chat with video content using natural language
- Get timestamped responses with clickable citations
- Context-aware conversations about video content

### 2. **Visual Search with Natural Language**
- Search for objects, people, or scenes within video frames
- Natural language queries (e.g., "red car", "person speaking")
- Semantic detection with confidence scoring
- Jump to specific moments in the video timeline

### 3. **Intelligent Section Breakdown**
- Automatic video segmentation into 5-minute sections
- AI-generated section titles and descriptions
- Hyperlinked timestamps for easy navigation
- Key topics extraction for each section

### 4. **Advanced Video Understanding**
- Powered by Gemini 2.5 Pro/Flash for enhanced video analysis
- Temporal counting and moment retrieval capabilities
- Support for videos up to 6 hours with configurable resolution
- Frame-by-frame content analysis and description

## Recent Improvements

### Visual Search False Positive Fix
- **Problem**: Search for "car" returned false positives from partial word matches (e.g., "card")
- **Solution**: Implemented regex word boundary detection for exact word matching
- **Result**: 100% accurate search results with no false positives

### Enhanced Search Accuracy
- Word boundary detection prevents partial matches
- Strict semantic validation with confidence thresholds
- Disabled pattern-based fallbacks that generated fake results
- Comprehensive logging for debugging and transparency

## Technology Stack

### Backend
- **FastAPI** - High-performance Python web framework
- **SQLAlchemy** - Database ORM with SQLite
- **Google Gemini 2.5** - Advanced multimodal AI for video understanding
- **ChromaDB** - Vector database for semantic search
- **FFmpeg** - Video processing and frame extraction
- **yt-dlp** - YouTube video downloading

### Frontend
- **React + TypeScript** - Modern web interface
- **Tailwind CSS** - Utility-first styling
- **Vite** - Fast development and build tool
- **Lucide React** - Beautiful icons

## Prerequisites

- Python 3.8+
- Node.js 16+
- FFmpeg installed
- Google Gemini API key




<img width="1377" alt="Screenshot 2025-06-05 at 7 39 25 PM" src="https://github.com/user-attachments/assets/111b35c4-3cfb-4ebc-82d4-0d17d00f8e12" />
