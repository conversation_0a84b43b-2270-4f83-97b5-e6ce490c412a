
### 1. **AI Video Chat**
- Upload video files or provide YouTube links
- Chat with video content using natural language
- Get timestamped responses with clickable citations
- Context-aware conversations about video content

### 2. **Visual Search with Natural Language**
- Search for objects, people, or scenes within video frames
- Natural language queries (e.g., "red car", "person speaking")
- Semantic detection with confidence scoring
- Jump to specific moments in the video timeline

### 3. **Intelligent Section Breakdown**
- Automatic video segmentation into 5-minute sections
- AI-generated section titles and descriptions
- Hyperlinked timestamps for easy navigation
- Key topics extraction for each section

### 4. **Advanced Video Understanding**
- Powered by Gemini 2.5 Pro/Flash for enhanced video analysis
- Temporal counting and moment retrieval capabilities
- Support for videos up to 6 hours with configurable resolution
- Frame-by-frame content analysis and description

## Recent Improvements

### Visual Search False Positive Fix
- **Problem**: Search for "car" returned false positives from partial word matches (e.g., "card")
- **Solution**: Implemented regex word boundary detection for exact word matching
- **Result**: 100% accurate search results with no false positives

### Enhanced Search Accuracy
- Word boundary detection prevents partial matches
- Strict semantic validation with confidence thresholds
- Disabled pattern-based fallbacks that generated fake results
- Comprehensive logging for debugging and transparency

## Technology Stack

### Backend
- **FastAPI** - High-performance Python web framework
- **SQLAlchemy** - Database ORM with SQLite
- **Google Gemini 2.5** - Advanced multimodal AI for video understanding
- **ChromaDB** - Vector database for semantic search
- **FFmpeg** - Video processing and frame extraction
- **yt-dlp** - YouTube video downloading

### Frontend
- **React + TypeScript** - Modern web interface
- **Tailwind CSS** - Utility-first styling
- **Vite** - Fast development and build tool
- **Lucide React** - Beautiful icons

## Prerequisites

- Python 3.8+
- Node.js 16+
- FFmpeg installed
- Google Gemini API key




<img width="1377" alt="Screenshot 2025-06-05 at 7 39 25 PM" src="https://github.com/user-attachments/assets/111b35c4-3cfb-4ebc-82d4-0d17d00f8e12" />
