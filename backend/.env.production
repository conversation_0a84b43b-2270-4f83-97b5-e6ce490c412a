# Fast Video Search Production Environment Configuration
# Copy to .env for production deployment

# =============================================================================
# API Keys
# =============================================================================
GEMINI_API_KEY=your_gemini_api_key_here

# =============================================================================
# Database Configuration
# =============================================================================
DATABASE_URL=sqlite:///./videochat.db

# =============================================================================
# Redis Configuration (for caching and performance)
# =============================================================================
REDIS_URL=redis://redis:6379/0
REDIS_TTL_SECONDS=86400

# =============================================================================
# Fast Search Configuration (for sub-2s performance)
# =============================================================================

# Auto-indexing
AUTO_INDEX_UPLOADS=true
MAX_INDEXING_TIME_SECONDS=15

# Frame extraction
FRAME_SAMPLING_INTERVAL=5
FFMPEG_SCALE=512:-1
FFMPEG_QUALITY=4

# CLIP & Vector Search
CLIP_MODEL=ViT-B/32
CLIP_BATCH_SIZE=32
ENABLE_HALF_PRECISION=true
FAISS_INDEX_TYPE=HNSW32
FAISS_SEARCH_K=200

# Performance targets
TARGET_SEARCH_LATENCY_MS=20
MAX_FRAMES_IN_MEMORY=100
CLEANUP_FRAMES_AFTER_ENCODING=true

# =============================================================================
# Shared Storage (for multi-instance deployment)
# =============================================================================
SHARED_STORAGE_TYPE=local  # local, s3, weaviate, pinecone

# S3 Configuration (if using S3 for shared storage)
# AWS_ACCESS_KEY_ID=your_aws_access_key
# AWS_SECRET_ACCESS_KEY=your_aws_secret_key
# AWS_REGION=us-east-1
# S3_BUCKET_NAME=videosearch-indices

# Weaviate Configuration (if using Weaviate)
# WEAVIATE_URL=http://weaviate:8080
# WEAVIATE_API_KEY=your_weaviate_key

# Pinecone Configuration (if using Pinecone)
# PINECONE_API_KEY=your_pinecone_key
# PINECONE_ENVIRONMENT=your_pinecone_env

# =============================================================================
# Server Configuration
# =============================================================================
HOST=0.0.0.0
PORT=8002
DEBUG=false

# =============================================================================
# File Storage
# =============================================================================
UPLOAD_DIR=uploads
INDICES_DIR=indices
THUMBNAILS_DIR=thumbnails

# =============================================================================
# Performance Tuning (advanced)
# =============================================================================

# Concurrency
MAX_CONCURRENT_GEMINI_CALLS=8
MAX_CONCURRENT_CLIP_CALLS=4

# Memory management
MAX_CACHE_SIZE=10000

# Legacy compatibility
FRAME_SAMPLING_STRIDE=30
MAX_SEARCH_RESULTS=20
MAX_CONCURRENT_REQUESTS=10
