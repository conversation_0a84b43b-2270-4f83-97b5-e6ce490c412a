from fastapi import <PERSON><PERSON><PERSON>, HTTPException, UploadFile, File, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from fastapi.staticfiles import StaticFiles
import os
from dotenv import load_dotenv
import logging
from contextlib import asynccontextmanager

from app.api.routes import video, chat, search
from app.core.config import settings
from app.core.database import engine, Base
from app.services.video_processor import VideoProcessor
from app.services.gemini_service import GeminiService

# Try to import streaming routes
try:
    from app.api.routes import streaming
    STREAMING_AVAILABLE = True
except ImportError:
    STREAMING_AVAILABLE = False

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Log streaming availability
if not STREAMING_AVAILABLE:
    logger.warning("Streaming routes not available - performance optimizations disabled")

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    # Startup
    logger.info("Starting VideoChat AI Backend...")
    
    try:
        # Create database tables
        Base.metadata.create_all(bind=engine)
        logger.info("Database tables created")
        
        # Create required directories
        for directory in ["thumbnails", "uploads", "uploads/frames", "uploads/youtube", "indices", "chroma_db"]:
            os.makedirs(directory, exist_ok=True)
        logger.info("Required directories created")

        # Mount static files after directories are created
        app.mount("/api/thumbnails", StaticFiles(directory="thumbnails"), name="thumbnails")
        app.mount("/api/frames", StaticFiles(directory="uploads/frames"), name="frames")
        # Note: /api/videos is handled by custom endpoint below for better video serving
        logger.info("Static file routes mounted")
        
        # Initialize services with error handling
        try:
            video_processor = VideoProcessor()
            app.state.video_processor = video_processor
            logger.info("Video processor initialized")
        except Exception as e:
            logger.warning(f"Failed to initialize video processor: {e}")
            app.state.video_processor = None
            
        try:
            gemini_service = GeminiService()
            app.state.gemini_service = gemini_service
            logger.info("Gemini service initialized")
        except Exception as e:
            logger.warning(f"Failed to initialize Gemini service: {e}")
            app.state.gemini_service = None
        
        logger.info("VideoSearch API startup complete!")
        
    except Exception as e:
        logger.error(f"Startup error: {e}")
        # Continue anyway to allow health checks
    
    yield
    
    # Shutdown
    logger.info("Shutting down VideoSearch API...")

# Create FastAPI app
app = FastAPI(
    title="VideoSearch API",
    description="AI-powered video analysis system with chat and visual search capabilities",
    version="1.0.0",
    lifespan=lifespan
)

# Add CORS middleware - Allow all origins for now
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Temporarily allow all origins
    allow_credentials=False,  # Must be False when allow_origins=["*"]
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(video.router, prefix="/api/v1/video", tags=["video"])
app.include_router(chat.router, prefix="/api/v1/chat", tags=["chat"])
app.include_router(search.router, prefix="/api/v1/search", tags=["search"])

# Include streaming routes if available
if STREAMING_AVAILABLE:
    app.include_router(streaming.router, prefix="/api/v1/streaming", tags=["streaming"])
    logger.info("Streaming routes enabled - performance optimizations available!")
else:
    logger.warning("Streaming routes disabled - using fallback methods")

# Static files will be mounted after directories are created in lifespan

@app.get("/")
async def root():
    """Health check endpoint"""
    return {
        "message": "VideoSearch API is running!",
        "version": "1.0.1",
        "status": "healthy",
        "deployment": "render-fixed"
    }

@app.get("/health")
async def health_check():
    """Detailed health check"""
    return {"status": "healthy"}

if __name__ == "__main__":
    import uvicorn
    
    # Get settings with fallbacks
    host = os.getenv("HOST", "0.0.0.0")
    port = int(os.getenv("PORT", "8080"))
    
    logger.info(f"Starting server on {host}:{port}")
    
    uvicorn.run(
        app,
        host=host,
        port=port,
        log_level="info"
    )
