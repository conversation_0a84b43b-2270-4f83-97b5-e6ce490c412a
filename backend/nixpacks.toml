[phases.setup]
nixPkgs = ['python311', 'pip', 'ffmpeg', 'git', 'curl']

[phases.install]
cmds = [
    'pip install --upgrade pip',
    'pip install torch torchvision --index-url https://download.pytorch.org/whl/cpu',
    'pip install git+https://github.com/openai/CLIP.git',
    'pip install -r requirements.txt'
]

[phases.build]
cmds = ['mkdir -p uploads indices thumbnails']

[start]
cmd = 'python main.py'

[variables]
PYTHONPATH = '/app'
PORT = '8002'
HOST = '0.0.0.0'
