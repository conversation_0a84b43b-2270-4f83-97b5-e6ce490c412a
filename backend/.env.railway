# Railway Production Environment Configuration
# Copy these variables to Railway dashboard

# =============================================================================
# Required: API Keys
# =============================================================================
GEMINI_API_KEY=your_gemini_api_key_here

# =============================================================================
# Railway Managed Services (Auto-configured)
# =============================================================================
DATABASE_URL=${{Postgres.DATABASE_URL}}
REDIS_URL=${{Redis.REDIS_URL}}

# =============================================================================
# Server Configuration
# =============================================================================
HOST=0.0.0.0
PORT=${{PORT}}
DEBUG=false

# =============================================================================
# Performance Settings
# =============================================================================
AUTO_INDEX_UPLOADS=true
MAX_INDEXING_TIME_SECONDS=15
FRAME_SAMPLING_INTERVAL=5
FFMPEG_SCALE=512:-1
FFMPEG_QUALITY=4
CLIP_MODEL=ViT-B/32
CLIP_BATCH_SIZE=32
ENABLE_HALF_PRECISION=true
FAISS_INDEX_TYPE=HNSW32
FAISS_SEARCH_K=200
TARGET_SEARCH_LATENCY_MS=20
MAX_FRAMES_IN_MEMORY=100
CLEANUP_FRAMES_AFTER_ENCODING=true

# =============================================================================
# File Storage
# =============================================================================
UPLOAD_DIR=uploads
INDICES_DIR=indices
THUMBNAILS_DIR=thumbnails

# =============================================================================
# Concurrency & Performance
# =============================================================================
MAX_CONCURRENT_GEMINI_CALLS=8
MAX_CONCURRENT_CLIP_CALLS=4
MAX_CACHE_SIZE=10000
REDIS_TTL_SECONDS=86400
