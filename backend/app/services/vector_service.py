
"""
Vector Service - Now powered by CLIP + Faiss for high-performance search
Replaces obsolete Chroma implementation with fast local indexing
"""

import logging
from typing import List, Dict, Any, Optional

from .clip_indexer import clip_indexer
from .redis_cache import redis_cache

logger = logging.getLogger(__name__)

class VectorService:
    """High-performance vector service using CLIP + Faiss"""

    def __init__(self):
        self.available = clip_indexer.is_available()
        if self.available:
            logger.info("Vector service initialized with CLIP + Faiss")
        else:
            logger.warning("Vector service initialized in fallback mode (CLIP not available)")

    async def add_video_embeddings(self, video_id: int, frames_data: List[Dict], metadata: Optional[Dict] = None):
        """Add video frame embeddings using CLIP + Faiss"""
        try:
            if not self.available:
                logger.warning(f"CLIP not available - skipping embedding for video {video_id}")
                return {"status": "skipped", "method": "clip_unavailable"}

            success = await clip_indexer.index_video_frames(video_id, frames_data)

            if success:
                logger.info(f"Successfully indexed {len(frames_data)} frames for video {video_id}")
                return {"status": "indexed", "method": "clip_faiss", "frame_count": len(frames_data)}
            else:
                logger.error(f"Failed to index frames for video {video_id}")
                return {"status": "error", "error": "CLIP indexing failed"}

        except Exception as e:
            logger.error(f"Error indexing video embeddings: {e}")
            return {"status": "error", "error": str(e)}

    async def search_similar(self, query: str, video_id: Optional[int] = None, limit: int = 10) -> List[Dict[str, Any]]:
        """Search for similar frames using CLIP + Faiss"""
        try:
            if not self.available or video_id is None:
                logger.warning("CLIP not available or no video_id - returning empty results")
                return []

            # Check cache first
            cached_results = await redis_cache.get_search_results(video_id, query)
            if cached_results:
                logger.info(f"Cache hit for query '{query}' in video {video_id}")
                return cached_results[:limit]

            # Search using CLIP
            results = await clip_indexer.search_similar_frames(video_id, query, k=limit)

            # Cache results
            if results:
                await redis_cache.set_search_results(video_id, query, results)

            return results

        except Exception as e:
            logger.error(f"Error in vector search: {e}")
            return []

    async def search_transcript(self, query: str, video_id: Optional[int] = None, limit: int = 10) -> List[Dict[str, Any]]:
        """Search transcript (delegated to transcript service)"""
        try:
            logger.info(f"Transcript search for: {query} (delegated to transcript service)")
            # This would integrate with transcript search if needed
            return []
        except Exception as e:
            logger.error(f"Error in transcript search: {e}")
            return []

    def get_status(self) -> Dict[str, Any]:
        """Get service status"""
        return {
            "available": self.available,
            "type": "clip_faiss" if self.available else "fallback",
            "features": ["clip_indexing", "faiss_search", "redis_cache"] if self.available else ["basic_fallback"]
        }

    def cleanup_video_index(self, video_id: int):
        """Clean up index for a video"""
        try:
            if self.available:
                clip_indexer.cleanup_index(video_id)
            logger.info(f"Cleaned up vector index for video {video_id}")
        except Exception as e:
            logger.error(f"Error cleaning up vector index: {e}")