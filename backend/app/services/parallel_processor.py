"""
Parallel Processing Service for Video Search
Implements concurrent frame analysis, caching, and result streaming
"""

import asyncio
import hashlib
import time
import logging
from typing import List, Dict, Optional, Callable, Any
from concurrent.futures import ThreadPoolExecutor
from dataclasses import dataclass
import json

from ..core.config import settings

logger = logging.getLogger(__name__)

@dataclass
class ProcessingTask:
    """Represents a single processing task"""
    task_id: str
    data: Any
    priority: int = 0
    created_at: float = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = time.time()

class ParallelProcessor:
    """High-performance parallel processor for video search operations"""
    
    def __init__(self):
        self.gemini_semaphore = asyncio.Semaphore(settings.MAX_CONCURRENT_GEMINI_CALLS)
        self.clip_semaphore = asyncio.Semaphore(settings.MAX_CONCURRENT_CLIP_CALLS)
        self.thread_executor = ThreadPoolExecutor(max_workers=8)
        
        # Caching
        self.frame_cache: Dict[str, Dict] = {}
        self.query_cache: Dict[str, List[Dict]] = {}
        self.max_cache_size = settings.MAX_CACHE_SIZE
        
        # Statistics
        self.stats = {
            'cache_hits': 0,
            'cache_misses': 0,
            'total_requests': 0,
            'avg_processing_time': 0.0
        }
        
    async def process_frames_parallel(self, 
                                    frames: List[Dict], 
                                    query: str,
                                    processor_func: Callable,
                                    progress_callback: Optional[Callable] = None,
                                    enable_early_termination: bool = True) -> List[Dict]:
        """
        Process frames in parallel with intelligent batching and caching
        
        Args:
            frames: List of frame data
            query: Search query
            processor_func: Function to process each frame
            progress_callback: Callback for progress updates
            enable_early_termination: Whether to stop early for simple queries
        """
        start_time = time.time()
        results = []
        processed_count = 0
        
        # Check cache first
        cache_key = self._get_query_cache_key(query, len(frames))
        cached_results = self._get_cached_results(cache_key)
        if cached_results:
            logger.info(f"Cache hit for query: {query}")
            self.stats['cache_hits'] += 1
            return cached_results
            
        self.stats['cache_misses'] += 1
        self.stats['total_requests'] += 1
        
        # Determine processing strategy
        query_type = self._detect_query_type(query)
        use_early_termination = enable_early_termination and query_type in ["simple", "text"]
        confidence_threshold = 0.7 if query_type in ["person", "object"] else 0.5
        
        logger.info(f"Processing {len(frames)} frames for '{query}' (type: {query_type})")
        
        # Create processing tasks
        tasks = []
        for i, frame_data in enumerate(frames):
            task = ProcessingTask(
                task_id=f"frame_{i}",
                data={
                    'frame_data': frame_data,
                    'query': query,
                    'index': i,
                    'processor_func': processor_func
                },
                priority=self._calculate_priority(frame_data, query_type)
            )
            tasks.append(task)
            
        # Sort by priority (higher priority first)
        tasks.sort(key=lambda x: x.priority, reverse=True)
        
        # Process in batches with controlled concurrency
        batch_size = min(settings.MAX_CONCURRENT_GEMINI_CALLS, len(tasks))
        
        for i in range(0, len(tasks), batch_size):
            batch = tasks[i:i + batch_size]
            
            # Process batch concurrently
            batch_results = await asyncio.gather(
                *[self._process_single_frame(task) for task in batch],
                return_exceptions=True
            )
            
            # Process results
            for task, result in zip(batch, batch_results):
                if isinstance(result, Exception):
                    logger.error(f"Error processing frame {task.task_id}: {result}")
                    continue
                    
                processed_count += 1
                
                # Update progress
                if progress_callback:
                    await progress_callback(processed_count, len(frames))
                
                # Check if we found a match
                if result and result.get('match', False):
                    confidence = result.get('confidence', 0.0)
                    
                    if confidence >= confidence_threshold:
                        results.append({
                            'timestamp': task.data['frame_data']['timestamp'],
                            'confidence': confidence,
                            'analysis': result,
                            'frame_index': task.data['index']
                        })
                        
                        # Early termination for simple queries
                        if use_early_termination and len(results) >= 3:
                            logger.info(f"Early termination: Found {len(results)} matches")
                            break
            
            # Break outer loop if early termination triggered
            if use_early_termination and len(results) >= 3:
                break
                
        # Cache results
        processing_time = time.time() - start_time
        self._cache_results(cache_key, results, processing_time)
        
        # Update statistics
        self._update_stats(processing_time)
        
        logger.info(f"Processed {processed_count}/{len(frames)} frames in {processing_time:.2f}s, found {len(results)} matches")
        
        return results
        
    async def _process_single_frame(self, task: ProcessingTask) -> Optional[Dict]:
        """Process a single frame with caching and error handling"""
        frame_data = task.data['frame_data']
        query = task.data['query']
        processor_func = task.data['processor_func']
        
        # Check frame-level cache
        frame_hash = self._get_frame_hash(frame_data)
        cache_key = f"frame_{frame_hash}_{hashlib.md5(query.encode()).hexdigest()[:8]}"
        
        cached_result = self._get_cached_frame_analysis(cache_key)
        if cached_result:
            return cached_result
            
        # Process with semaphore control
        async with self.gemini_semaphore:
            try:
                result = await processor_func(frame_data, query)
                
                # Cache the result
                self._cache_frame_analysis(cache_key, result)
                
                return result
                
            except Exception as e:
                logger.error(f"Error processing frame: {e}")
                return None
                
    def _detect_query_type(self, query: str) -> str:
        """Detect query type for optimization strategy"""
        query_lower = query.lower()
        
        # Person queries
        if any(word in query_lower for word in ['person', 'people', 'man', 'woman', 'face', 'speaker']):
            return "person"
            
        # Object queries
        if any(word in query_lower for word in ['car', 'object', 'item', 'thing', 'microphone', 'phone']):
            return "object"
            
        # Color queries
        if any(word in query_lower for word in ['red', 'blue', 'green', 'yellow', 'black', 'white', 'color']):
            return "color"
            
        # Text queries
        if any(word in query_lower for word in ['text', 'word', 'sign', 'title', 'caption']):
            return "text"
            
        # Simple queries
        if len(query.split()) <= 2:
            return "simple"
            
        return "complex"
        
    def _calculate_priority(self, frame_data: Dict, query_type: str) -> int:
        """Calculate processing priority for frame"""
        base_priority = 100
        
        # Prioritize frames with scene changes
        if frame_data.get('scene_change', False):
            base_priority += 50
            
        # Prioritize frames with good quality
        quality_score = frame_data.get('quality_score', 0.5)
        base_priority += int(quality_score * 30)
        
        # Adjust based on query type
        if query_type == "person" and frame_data.get('has_faces', False):
            base_priority += 40
        elif query_type == "text" and frame_data.get('has_text', False):
            base_priority += 40
            
        return base_priority
        
    def _get_query_cache_key(self, query: str, frame_count: int) -> str:
        """Generate cache key for query results"""
        query_hash = hashlib.md5(query.encode()).hexdigest()[:12]
        return f"query_{query_hash}_{frame_count}"
        
    def _get_frame_hash(self, frame_data: Dict) -> str:
        """Generate hash for frame data"""
        # Use timestamp and basic frame info for hashing
        frame_info = f"{frame_data.get('timestamp', 0):.1f}_{frame_data.get('frame_path', '')}"
        return hashlib.md5(frame_info.encode()).hexdigest()[:12]
        
    def _get_cached_results(self, cache_key: str) -> Optional[List[Dict]]:
        """Get cached query results"""
        return self.query_cache.get(cache_key)
        
    def _cache_results(self, cache_key: str, results: List[Dict], processing_time: float) -> None:
        """Cache query results with TTL"""
        if len(self.query_cache) >= self.max_cache_size:
            # Remove oldest entries
            oldest_keys = list(self.query_cache.keys())[:self.max_cache_size // 4]
            for key in oldest_keys:
                del self.query_cache[key]
                
        self.query_cache[cache_key] = {
            'results': results,
            'cached_at': time.time(),
            'processing_time': processing_time
        }
        
    def _get_cached_frame_analysis(self, cache_key: str) -> Optional[Dict]:
        """Get cached frame analysis"""
        cached = self.frame_cache.get(cache_key)
        if cached and time.time() - cached['cached_at'] < settings.CACHE_TTL_HOURS * 3600:
            return cached['analysis']
        return None
        
    def _cache_frame_analysis(self, cache_key: str, analysis: Dict) -> None:
        """Cache frame analysis result"""
        if len(self.frame_cache) >= self.max_cache_size:
            # Remove oldest entries
            oldest_keys = list(self.frame_cache.keys())[:self.max_cache_size // 4]
            for key in oldest_keys:
                del self.frame_cache[key]
                
        self.frame_cache[cache_key] = {
            'analysis': analysis,
            'cached_at': time.time()
        }
        
    def _update_stats(self, processing_time: float) -> None:
        """Update processing statistics"""
        total_requests = self.stats['total_requests']
        current_avg = self.stats['avg_processing_time']
        
        # Update rolling average
        self.stats['avg_processing_time'] = (
            (current_avg * (total_requests - 1) + processing_time) / total_requests
        )
        
    def get_stats(self) -> Dict:
        """Get processing statistics"""
        cache_total = self.stats['cache_hits'] + self.stats['cache_misses']
        cache_hit_rate = (self.stats['cache_hits'] / cache_total * 100) if cache_total > 0 else 0
        
        return {
            **self.stats,
            'cache_hit_rate': cache_hit_rate,
            'active_frame_cache_size': len(self.frame_cache),
            'active_query_cache_size': len(self.query_cache)
        }

# Global instance
parallel_processor = ParallelProcessor()
