"""
Fast Frame Extractor using FFmpeg
Replaces slow OpenCV frame-by-frame extraction with fast FFmpeg batch processing
"""

import asyncio
import subprocess
import os
import logging
import tempfile
import shutil
from typing import List, Dict, Optional
from pathlib import Path

from ..core.config import settings

logger = logging.getLogger(__name__)

class FastFrameExtractor:
    """High-performance frame extraction using FFmpeg"""
    
    def __init__(self):
        self.temp_dirs = []
        
    async def extract_frames_fast(self, video_path: str, video_id: int) -> List[Dict]:
        """
        Fast frame extraction using FFmpeg
        
        Args:
            video_path: Path to video file
            video_id: Video ID for organizing output
            
        Returns:
            List of frame metadata dicts
        """
        
        # Create output directory
        out_dir = os.path.join(settings.INDICES_DIR, f"frames_{video_id}")
        os.makedirs(out_dir, exist_ok=True)
        
        try:
            # Extract frames with FFmpeg
            frames_data = await self._ffmpeg_extract(video_path, out_dir, video_id)
            
            logger.info(f"Fast extracted {len(frames_data)} frames for video {video_id}")
            return frames_data
            
        except Exception as e:
            logger.error(f"Fast frame extraction failed for video {video_id}: {e}")
            # Cleanup on failure
            if os.path.exists(out_dir):
                shutil.rmtree(out_dir)
            return []
    
    async def _ffmpeg_extract(self, video_path: str, out_dir: str, video_id: int) -> List[Dict]:
        """Extract frames using FFmpeg subprocess"""
        
        # FFmpeg command for fast frame extraction
        cmd = [
            'ffmpeg',
            '-i', video_path,
            '-vf', f'fps=1/{settings.FRAME_SAMPLING_INTERVAL},scale={settings.FFMPEG_SCALE}',
            '-q:v', settings.FFMPEG_QUALITY,
            '-f', 'image2',
            f'{out_dir}/%06d.jpg'
        ]
        
        logger.info(f"Running FFmpeg: {' '.join(cmd)}")
        
        # Run FFmpeg asynchronously
        process = await asyncio.create_subprocess_exec(
            *cmd,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        
        stdout, stderr = await process.communicate()
        
        if process.returncode != 0:
            error_msg = stderr.decode() if stderr else "Unknown FFmpeg error"
            logger.error(f"FFmpeg failed: {error_msg}")
            raise RuntimeError(f"FFmpeg extraction failed: {error_msg}")
        
        # Get video info for timestamps
        duration = await self._get_video_duration(video_path)
        
        # Collect frame files and create metadata
        frame_files = sorted([f for f in os.listdir(out_dir) if f.endswith('.jpg')])
        frames_data = []
        
        for i, frame_file in enumerate(frame_files):
            frame_path = os.path.join(out_dir, frame_file)
            
            # Calculate timestamp (1 frame every FRAME_SAMPLING_INTERVAL seconds)
            timestamp = i * settings.FRAME_SAMPLING_INTERVAL
            
            frames_data.append({
                'frame_path': frame_path,
                'timestamp': timestamp,
                'frame_index': i,
                'video_id': video_id,
                'relative_path': f"frames_{video_id}/{frame_file}"
            })
        
        logger.info(f"FFmpeg extracted {len(frames_data)} frames in {duration:.1f}s video")
        return frames_data
    
    async def _get_video_duration(self, video_path: str) -> float:
        """Get video duration using FFprobe"""
        
        cmd = [
            'ffprobe',
            '-v', 'quiet',
            '-print_format', 'json',
            '-show_format',
            video_path
        ]
        
        try:
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await process.communicate()
            
            if process.returncode == 0:
                import json
                data = json.loads(stdout.decode())
                return float(data['format']['duration'])
            else:
                logger.warning(f"FFprobe failed: {stderr.decode()}")
                return 0.0
                
        except Exception as e:
            logger.warning(f"Could not get video duration: {e}")
            return 0.0
    
    def cleanup_frames(self, video_id: int):
        """Clean up extracted frames for a video"""
        
        out_dir = os.path.join(settings.INDICES_DIR, f"frames_{video_id}")
        if os.path.exists(out_dir):
            try:
                shutil.rmtree(out_dir)
                logger.info(f"Cleaned up frames for video {video_id}")
            except Exception as e:
                logger.error(f"Failed to cleanup frames for video {video_id}: {e}")

class MemoryEfficientFrameLoader:
    """Memory-efficient frame loading for CLIP processing"""
    
    def __init__(self, max_frames_in_memory: int = None):
        self.max_frames_in_memory = max_frames_in_memory or settings.MAX_FRAMES_IN_MEMORY
        
    async def load_frames_batch(self, frame_paths: List[str], batch_size: int = None) -> List:
        """
        Load frames in batches to avoid OOM
        
        Args:
            frame_paths: List of frame file paths
            batch_size: Batch size for loading
            
        Yields:
            Batches of loaded frame tensors
        """
        
        batch_size = batch_size or settings.CLIP_BATCH_SIZE
        
        for i in range(0, len(frame_paths), batch_size):
            batch_paths = frame_paths[i:i + batch_size]
            
            # Load batch of frames
            frames = []
            for frame_path in batch_paths:
                try:
                    from PIL import Image
                    image = Image.open(frame_path)
                    frames.append(image)
                except Exception as e:
                    logger.warning(f"Failed to load frame {frame_path}: {e}")
                    continue
            
            yield frames
            
            # Memory cleanup
            del frames
    
    def estimate_memory_usage(self, num_frames: int, frame_size: tuple = (512, 512)) -> float:
        """Estimate memory usage in MB for given number of frames"""
        
        # Rough estimate: 3 channels * width * height * 4 bytes (float32)
        bytes_per_frame = 3 * frame_size[0] * frame_size[1] * 4
        total_bytes = num_frames * bytes_per_frame
        return total_bytes / (1024 * 1024)  # Convert to MB

# Global instances
fast_extractor = FastFrameExtractor()
memory_loader = MemoryEfficientFrameLoader()

async def run_cpu_task(func, *args, **kwargs):
    """Run CPU-intensive task in thread pool"""
    
    loop = asyncio.get_event_loop()
    return await loop.run_in_executor(None, func, *args, **kwargs)

def check_ffmpeg_available() -> bool:
    """Check if FFmpeg is available on the system"""
    
    try:
        result = subprocess.run(['ffmpeg', '-version'], 
                              capture_output=True, 
                              text=True, 
                              timeout=5)
        return result.returncode == 0
    except (subprocess.TimeoutExpired, FileNotFoundError):
        return False

def check_ffprobe_available() -> bool:
    """Check if FFprobe is available on the system"""
    
    try:
        result = subprocess.run(['ffprobe', '-version'], 
                              capture_output=True, 
                              text=True, 
                              timeout=5)
        return result.returncode == 0
    except (subprocess.TimeoutExpired, FileNotFoundError):
        return False

# Check availability on import
FFMPEG_AVAILABLE = check_ffmpeg_available()
FFPROBE_AVAILABLE = check_ffprobe_available()

if not FFMPEG_AVAILABLE:
    logger.warning("FFmpeg not found - falling back to OpenCV frame extraction")
if not FFPROBE_AVAILABLE:
    logger.warning("FFprobe not found - video duration detection may fail")
