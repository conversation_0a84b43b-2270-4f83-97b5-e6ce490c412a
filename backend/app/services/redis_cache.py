"""
Redis Cache Service
Persistent caching for search results and Gemini responses
"""

import json
import hashlib
import logging
from typing import Any, Optional, Dict, List
import asyncio

from ..core.config import settings

logger = logging.getLogger(__name__)

class RedisCache:
    """Redis-based cache for search results and API responses"""
    
    def __init__(self):
        self.redis_client = None
        self.redis_available = False
        self._initialized = False
    
    async def _init_redis(self):
        """Initialize Redis connection"""
        
        try:
            import redis.asyncio as redis
            
            self.redis_client = redis.from_url(
                settings.REDIS_URL,
                encoding="utf-8",
                decode_responses=True
            )
            
            # Test connection
            await self.redis_client.ping()
            self.redis_available = True
            
            logger.info(f"Redis cache initialized: {settings.REDIS_URL}")
            
        except ImportError:
            logger.warning("Redis not available - install redis package for caching")
        except Exception as e:
            logger.warning(f"Redis connection failed: {e}")
    
    def _make_key(self, prefix: str, *args) -> str:
        """Create cache key from prefix and arguments"""
        
        # Create hash from arguments
        key_data = ":".join(str(arg) for arg in args)
        key_hash = hashlib.md5(key_data.encode()).hexdigest()[:12]
        
        return f"{prefix}:{key_hash}"
    
    async def _ensure_initialized(self):
        """Ensure Redis is initialized"""
        if not self._initialized:
            await self._init_redis()
            self._initialized = True

    async def get_search_results(self, video_id: int, query: str) -> Optional[List[Dict]]:
        """Get cached search results"""

        await self._ensure_initialized()

        if not self.redis_available:
            return None
        
        try:
            key = self._make_key("search", video_id, query)
            cached_data = await self.redis_client.get(key)
            
            if cached_data:
                data = json.loads(cached_data)
                logger.debug(f"Cache hit for search: {video_id}:{query}")
                return data.get('results', [])
            
            return None
            
        except Exception as e:
            logger.error(f"Failed to get cached search results: {e}")
            return None
    
    async def set_search_results(self, video_id: int, query: str, results: List[Dict], ttl: int = None):
        """Cache search results"""

        await self._ensure_initialized()

        if not self.redis_available:
            return
        
        try:
            key = self._make_key("search", video_id, query)
            ttl = ttl or settings.REDIS_TTL_SECONDS
            
            cache_data = {
                'results': results,
                'cached_at': asyncio.get_event_loop().time(),
                'video_id': video_id,
                'query': query
            }
            
            await self.redis_client.setex(
                key, 
                ttl, 
                json.dumps(cache_data, default=str)
            )
            
            logger.debug(f"Cached search results: {video_id}:{query}")
            
        except Exception as e:
            logger.error(f"Failed to cache search results: {e}")
    
    async def get_gemini_response(self, video_id: int, query: str) -> Optional[Dict]:
        """Get cached Gemini native response"""
        
        if not self.redis_available:
            return None
        
        try:
            key = self._make_key("g_native", video_id, query)
            cached_data = await self.redis_client.get(key)
            
            if cached_data:
                data = json.loads(cached_data)
                logger.debug(f"Cache hit for Gemini: {video_id}:{query}")
                return data.get('response')
            
            return None
            
        except Exception as e:
            logger.error(f"Failed to get cached Gemini response: {e}")
            return None
    
    async def set_gemini_response(self, video_id: int, query: str, response: Dict, ttl: int = None):
        """Cache Gemini native response"""
        
        if not self.redis_available:
            return
        
        try:
            key = self._make_key("g_native", video_id, query)
            ttl = ttl or settings.REDIS_TTL_SECONDS
            
            cache_data = {
                'response': response,
                'cached_at': asyncio.get_event_loop().time(),
                'video_id': video_id,
                'query': query
            }
            
            await self.redis_client.setex(
                key,
                ttl,
                json.dumps(cache_data, default=str)
            )
            
            logger.debug(f"Cached Gemini response: {video_id}:{query}")
            
        except Exception as e:
            logger.error(f"Failed to cache Gemini response: {e}")
    
    async def get_frame_analysis(self, frame_path: str, query: str) -> Optional[Dict]:
        """Get cached frame analysis result"""
        
        if not self.redis_available:
            return None
        
        try:
            # Use frame path hash for key
            frame_hash = hashlib.md5(frame_path.encode()).hexdigest()[:8]
            key = self._make_key("frame", frame_hash, query)
            
            cached_data = await self.redis_client.get(key)
            
            if cached_data:
                data = json.loads(cached_data)
                logger.debug(f"Cache hit for frame analysis: {frame_hash}:{query}")
                return data.get('analysis')
            
            return None
            
        except Exception as e:
            logger.error(f"Failed to get cached frame analysis: {e}")
            return None
    
    async def set_frame_analysis(self, frame_path: str, query: str, analysis: Dict, ttl: int = None):
        """Cache frame analysis result"""
        
        if not self.redis_available:
            return
        
        try:
            frame_hash = hashlib.md5(frame_path.encode()).hexdigest()[:8]
            key = self._make_key("frame", frame_hash, query)
            ttl = ttl or settings.REDIS_TTL_SECONDS
            
            cache_data = {
                'analysis': analysis,
                'cached_at': asyncio.get_event_loop().time(),
                'frame_path': frame_path,
                'query': query
            }
            
            await self.redis_client.setex(
                key,
                ttl,
                json.dumps(cache_data, default=str)
            )
            
            logger.debug(f"Cached frame analysis: {frame_hash}:{query}")
            
        except Exception as e:
            logger.error(f"Failed to cache frame analysis: {e}")
    
    async def get_video_index_status(self, video_id: int) -> Optional[Dict]:
        """Get video indexing status"""
        
        if not self.redis_available:
            return None
        
        try:
            key = f"index_status:{video_id}"
            cached_data = await self.redis_client.get(key)
            
            if cached_data:
                return json.loads(cached_data)
            
            return None
            
        except Exception as e:
            logger.error(f"Failed to get index status: {e}")
            return None
    
    async def set_video_index_status(self, video_id: int, status: Dict, ttl: int = None):
        """Set video indexing status"""
        
        if not self.redis_available:
            return
        
        try:
            key = f"index_status:{video_id}"
            ttl = ttl or settings.REDIS_TTL_SECONDS
            
            await self.redis_client.setex(
                key,
                ttl,
                json.dumps(status, default=str)
            )
            
            logger.debug(f"Set index status for video {video_id}: {status.get('stage', 'unknown')}")
            
        except Exception as e:
            logger.error(f"Failed to set index status: {e}")
    
    async def invalidate_video_cache(self, video_id: int):
        """Invalidate all cache entries for a video"""
        
        if not self.redis_available:
            return
        
        try:
            # Find all keys related to this video
            patterns = [
                f"search:*{video_id}*",
                f"g_native:*{video_id}*",
                f"index_status:{video_id}"
            ]
            
            for pattern in patterns:
                keys = await self.redis_client.keys(pattern)
                if keys:
                    await self.redis_client.delete(*keys)
                    logger.info(f"Invalidated {len(keys)} cache entries for video {video_id}")
            
        except Exception as e:
            logger.error(f"Failed to invalidate cache for video {video_id}: {e}")
    
    async def get_cache_stats(self) -> Dict:
        """Get cache statistics"""
        
        if not self.redis_available:
            return {"available": False}
        
        try:
            info = await self.redis_client.info()
            
            return {
                "available": True,
                "connected_clients": info.get("connected_clients", 0),
                "used_memory": info.get("used_memory_human", "0B"),
                "keyspace_hits": info.get("keyspace_hits", 0),
                "keyspace_misses": info.get("keyspace_misses", 0),
                "hit_rate": self._calculate_hit_rate(
                    info.get("keyspace_hits", 0),
                    info.get("keyspace_misses", 0)
                )
            }
            
        except Exception as e:
            logger.error(f"Failed to get cache stats: {e}")
            return {"available": False, "error": str(e)}
    
    def _calculate_hit_rate(self, hits: int, misses: int) -> float:
        """Calculate cache hit rate percentage"""
        
        total = hits + misses
        if total == 0:
            return 0.0
        
        return (hits / total) * 100.0
    
    def is_available(self) -> bool:
        """Check if Redis cache is available"""
        return self.redis_available

# Global instance
redis_cache = RedisCache()
