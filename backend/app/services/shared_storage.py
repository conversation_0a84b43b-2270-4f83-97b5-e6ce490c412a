"""
Shared Storage Service for Multi-Instance Deployment
Supports S3, <PERSON><PERSON><PERSON>, Pinecone for distributed vector storage
"""

import logging
import os
import asyncio
from typing import Dict, List, Optional, Any
import pickle
import numpy as np

from ..core.config import settings

logger = logging.getLogger(__name__)

class SharedStorageService:
    """Shared storage for vector indices across multiple instances"""
    
    def __init__(self):
        self.storage_type = os.getenv("SHARED_STORAGE_TYPE", "local")  # local, s3, weaviate, pinecone
        self.available = False
        self.client = None
        
        # Initialize based on storage type
        asyncio.create_task(self._init_storage())
    
    async def _init_storage(self):
        """Initialize shared storage based on configuration"""
        
        try:
            if self.storage_type == "s3":
                await self._init_s3()
            elif self.storage_type == "weaviate":
                await self._init_weaviate()
            elif self.storage_type == "pinecone":
                await self._init_pinecone()
            else:
                # Local storage (default)
                self.available = True
                logger.info("Using local storage for vector indices")
                
        except Exception as e:
            logger.error(f"Failed to initialize shared storage ({self.storage_type}): {e}")
            self.available = False
    
    async def _init_s3(self):
        """Initialize S3 storage for vector indices"""
        
        try:
            import boto3
            
            self.client = boto3.client(
                's3',
                aws_access_key_id=os.getenv("AWS_ACCESS_KEY_ID"),
                aws_secret_access_key=os.getenv("AWS_SECRET_ACCESS_KEY"),
                region_name=os.getenv("AWS_REGION", "us-east-1")
            )
            
            self.bucket_name = os.getenv("S3_BUCKET_NAME", "videosearch-indices")
            
            # Test connection
            self.client.head_bucket(Bucket=self.bucket_name)
            
            self.available = True
            logger.info(f"S3 shared storage initialized: {self.bucket_name}")
            
        except ImportError:
            logger.error("boto3 not installed - install with: pip install boto3")
        except Exception as e:
            logger.error(f"S3 initialization failed: {e}")
    
    async def _init_weaviate(self):
        """Initialize Weaviate vector database"""
        
        try:
            import weaviate
            
            weaviate_url = os.getenv("WEAVIATE_URL", "http://localhost:8080")
            weaviate_key = os.getenv("WEAVIATE_API_KEY")
            
            if weaviate_key:
                auth_config = weaviate.AuthApiKey(api_key=weaviate_key)
                self.client = weaviate.Client(url=weaviate_url, auth_client_secret=auth_config)
            else:
                self.client = weaviate.Client(url=weaviate_url)
            
            # Test connection
            self.client.schema.get()
            
            # Create schema if it doesn't exist
            await self._create_weaviate_schema()
            
            self.available = True
            logger.info(f"Weaviate shared storage initialized: {weaviate_url}")
            
        except ImportError:
            logger.error("weaviate-client not installed - install with: pip install weaviate-client")
        except Exception as e:
            logger.error(f"Weaviate initialization failed: {e}")
    
    async def _init_pinecone(self):
        """Initialize Pinecone vector database"""
        
        try:
            import pinecone
            
            pinecone_key = os.getenv("PINECONE_API_KEY")
            pinecone_env = os.getenv("PINECONE_ENVIRONMENT")
            
            if not pinecone_key or not pinecone_env:
                raise ValueError("PINECONE_API_KEY and PINECONE_ENVIRONMENT required")
            
            pinecone.init(api_key=pinecone_key, environment=pinecone_env)
            
            # Create index if it doesn't exist
            index_name = "videosearch-frames"
            if index_name not in pinecone.list_indexes():
                pinecone.create_index(
                    name=index_name,
                    dimension=512,  # CLIP ViT-B/32 dimension
                    metric="cosine"
                )
            
            self.client = pinecone.Index(index_name)
            
            self.available = True
            logger.info(f"Pinecone shared storage initialized: {index_name}")
            
        except ImportError:
            logger.error("pinecone-client not installed - install with: pip install pinecone-client")
        except Exception as e:
            logger.error(f"Pinecone initialization failed: {e}")
    
    async def store_vector_index(self, video_id: int, embeddings: np.ndarray, metadata: List[Dict]) -> bool:
        """Store vector index in shared storage"""
        
        if not self.available:
            logger.warning("Shared storage not available - using local storage")
            return False
        
        try:
            if self.storage_type == "s3":
                return await self._store_s3(video_id, embeddings, metadata)
            elif self.storage_type == "weaviate":
                return await self._store_weaviate(video_id, embeddings, metadata)
            elif self.storage_type == "pinecone":
                return await self._store_pinecone(video_id, embeddings, metadata)
            else:
                return True  # Local storage handled by clip_indexer
                
        except Exception as e:
            logger.error(f"Failed to store vector index for video {video_id}: {e}")
            return False
    
    async def load_vector_index(self, video_id: int) -> tuple[Optional[np.ndarray], Optional[List[Dict]]]:
        """Load vector index from shared storage"""
        
        if not self.available:
            return None, None
        
        try:
            if self.storage_type == "s3":
                return await self._load_s3(video_id)
            elif self.storage_type == "weaviate":
                return await self._load_weaviate(video_id)
            elif self.storage_type == "pinecone":
                return await self._load_pinecone(video_id)
            else:
                return None, None  # Local storage handled by clip_indexer
                
        except Exception as e:
            logger.error(f"Failed to load vector index for video {video_id}: {e}")
            return None, None
    
    async def search_vectors(self, video_id: int, query_embedding: np.ndarray, k: int = 10) -> List[Dict]:
        """Search vectors in shared storage"""
        
        if not self.available:
            return []
        
        try:
            if self.storage_type == "weaviate":
                return await self._search_weaviate(video_id, query_embedding, k)
            elif self.storage_type == "pinecone":
                return await self._search_pinecone(video_id, query_embedding, k)
            else:
                return []  # S3 and local don't support direct search
                
        except Exception as e:
            logger.error(f"Failed to search vectors for video {video_id}: {e}")
            return []
    
    # S3 Implementation
    async def _store_s3(self, video_id: int, embeddings: np.ndarray, metadata: List[Dict]) -> bool:
        """Store embeddings and metadata in S3"""
        
        try:
            # Store embeddings
            embeddings_key = f"indices/{video_id}/embeddings.npy"
            embeddings_bytes = embeddings.tobytes()
            
            self.client.put_object(
                Bucket=self.bucket_name,
                Key=embeddings_key,
                Body=embeddings_bytes,
                ContentType="application/octet-stream"
            )
            
            # Store metadata
            metadata_key = f"indices/{video_id}/metadata.pkl"
            metadata_bytes = pickle.dumps(metadata)
            
            self.client.put_object(
                Bucket=self.bucket_name,
                Key=metadata_key,
                Body=metadata_bytes,
                ContentType="application/octet-stream"
            )
            
            logger.info(f"Stored vector index for video {video_id} in S3")
            return True
            
        except Exception as e:
            logger.error(f"S3 storage failed for video {video_id}: {e}")
            return False
    
    async def _load_s3(self, video_id: int) -> tuple[Optional[np.ndarray], Optional[List[Dict]]]:
        """Load embeddings and metadata from S3"""
        
        try:
            # Load embeddings
            embeddings_key = f"indices/{video_id}/embeddings.npy"
            embeddings_obj = self.client.get_object(Bucket=self.bucket_name, Key=embeddings_key)
            embeddings_bytes = embeddings_obj['Body'].read()
            embeddings = np.frombuffer(embeddings_bytes, dtype=np.float32)
            
            # Load metadata
            metadata_key = f"indices/{video_id}/metadata.pkl"
            metadata_obj = self.client.get_object(Bucket=self.bucket_name, Key=metadata_key)
            metadata_bytes = metadata_obj['Body'].read()
            metadata = pickle.loads(metadata_bytes)
            
            logger.info(f"Loaded vector index for video {video_id} from S3")
            return embeddings, metadata
            
        except Exception as e:
            logger.error(f"S3 load failed for video {video_id}: {e}")
            return None, None
    
    # Weaviate Implementation
    async def _create_weaviate_schema(self):
        """Create Weaviate schema for video frames"""
        
        schema = {
            "class": "VideoFrame",
            "description": "Video frame with CLIP embeddings",
            "properties": [
                {
                    "name": "video_id",
                    "dataType": ["int"],
                    "description": "Video ID"
                },
                {
                    "name": "timestamp",
                    "dataType": ["number"],
                    "description": "Frame timestamp in seconds"
                },
                {
                    "name": "frame_path",
                    "dataType": ["string"],
                    "description": "Path to frame image"
                },
                {
                    "name": "frame_index",
                    "dataType": ["int"],
                    "description": "Frame index in video"
                }
            ],
            "vectorizer": "none"  # We provide our own vectors
        }
        
        try:
            self.client.schema.create_class(schema)
            logger.info("Created Weaviate schema for VideoFrame")
        except Exception as e:
            if "already exists" not in str(e):
                logger.error(f"Failed to create Weaviate schema: {e}")
    
    async def _store_weaviate(self, video_id: int, embeddings: np.ndarray, metadata: List[Dict]) -> bool:
        """Store embeddings in Weaviate"""
        
        try:
            # Batch insert frames
            with self.client.batch as batch:
                for i, (embedding, meta) in enumerate(zip(embeddings, metadata)):
                    properties = {
                        "video_id": video_id,
                        "timestamp": meta.get("timestamp", 0),
                        "frame_path": meta.get("frame_path", ""),
                        "frame_index": meta.get("frame_index", i)
                    }
                    
                    batch.add_data_object(
                        data_object=properties,
                        class_name="VideoFrame",
                        vector=embedding.tolist()
                    )
            
            logger.info(f"Stored {len(embeddings)} frames for video {video_id} in Weaviate")
            return True
            
        except Exception as e:
            logger.error(f"Weaviate storage failed for video {video_id}: {e}")
            return False
    
    async def _search_weaviate(self, video_id: int, query_embedding: np.ndarray, k: int) -> List[Dict]:
        """Search similar frames in Weaviate"""
        
        try:
            result = (
                self.client.query
                .get("VideoFrame", ["video_id", "timestamp", "frame_path", "frame_index"])
                .with_near_vector({"vector": query_embedding.tolist()})
                .with_where({"path": ["video_id"], "operator": "Equal", "valueInt": video_id})
                .with_limit(k)
                .with_additional(["distance"])
                .do()
            )
            
            frames = result["data"]["Get"]["VideoFrame"]
            
            # Convert to standard format
            results = []
            for frame in frames:
                results.append({
                    "timestamp": frame["timestamp"],
                    "frame_path": frame["frame_path"],
                    "frame_index": frame["frame_index"],
                    "similarity_score": 1.0 - frame["_additional"]["distance"],  # Convert distance to similarity
                    "confidence": (1.0 - frame["_additional"]["distance"]) * 100
                })
            
            return results
            
        except Exception as e:
            logger.error(f"Weaviate search failed for video {video_id}: {e}")
            return []
    
    def is_available(self) -> bool:
        """Check if shared storage is available"""
        return self.available
    
    def get_storage_type(self) -> str:
        """Get current storage type"""
        return self.storage_type

# Global instance
shared_storage = SharedStorageService()
