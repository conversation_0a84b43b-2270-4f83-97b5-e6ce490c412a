import logging
import json
import os
import tempfile
from contextlib import contextmanager
import re
import asyncio
from typing import Optional, Dict

from yt_dlp import YoutubeDL

from app.core.config import settings

logger = logging.getLogger(__name__)

YOUTUBE_COOKIES_CONTENT = settings.YOUTUBE_COOKIES_CONTENT

@contextmanager
def _get_cookie_file_path():
    """Writes cookie content to a temporary file and returns the path, then cleans up."""
    if not YOUTUBE_COOKIES_CONTENT:
        yield None
        return
    
    cookie_filename = None
    try:
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.txt', encoding='utf-8') as tf:
            tf.write(YOUTUBE_COOKIES_CONTENT)
            cookie_filename = tf.name
        
        logger.info(f"Using YouTube cookies for authentication from temporary file.")
        yield cookie_filename
    
    finally:
        if cookie_filename and os.path.exists(cookie_filename):
            os.remove(cookie_filename)
            logger.info(f"Removed temporary cookie file.")


def _get_ydl_opts(cookie_file_path: Optional[str] = None, extra_opts: dict = {}) -> dict:
    """Create a base set of yt-dlp options, including robust anti-blocking measures."""
    base_opts = {
        'quiet': True,
        'no_warnings': True,
        'format': 'best[ext=mp4][height<=720]/best[height<=720]/best',
        'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'sleep_interval': 3,
        'max_sleep_interval': 10,
        'sleep_interval_requests': 2,
        'sleep_interval_subtitles': 3,
        'http_headers': {
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'en-us,en;q=0.5',
            'Accept-Encoding': 'gzip,deflate',
            'Accept-Charset': 'ISO-8859-1,utf-8;q=0.7,*;q=0.7',
            'Keep-Alive': '300',
            'Connection': 'keep-alive',
        },
        'retries': 5,
        'fragment_retries': 5,
        'socket_timeout': 30,
        'extractor_args': {
            'youtube': {
                'player_client': ['mweb', 'web'],
                'player_skip': ['webpage'],
            }
        },
        'geo_bypass': True,
        'geo_bypass_country': 'US',
    }

    if settings.PROXY_URL:
        base_opts['proxy'] = settings.PROXY_URL
        logger.info("Using proxy for yt-dlp requests.")
    
    if cookie_file_path:
        base_opts['cookiefile'] = cookie_file_path

    base_opts.update(extra_opts)
    return base_opts

def extract_video_id(url: str) -> Optional[str]:
    """Extract YouTube video ID from URL"""
    patterns = [
        r'(?:youtube\\.com\\/watch\\?v=|youtu\\.be\\/|youtube\\.com\\/embed\\/)([^&\\n?#]+)',
        r'youtube\\.com\\/watch\\?.*v=([^&\\n?#]+)',
    ]
    
    for pattern in patterns:
        match = re.search(pattern, url)
        if match:
            return match.group(1)
    
    return None
    
async def get_video_info(video_id: str) -> Dict:
    """Get basic video information without downloading"""
    with _get_cookie_file_path() as cookie_file_path:
        ydl_opts = _get_ydl_opts(cookie_file_path, {'skip_download': True})
        
        try:
            with YoutubeDL(ydl_opts) as ydl:
                loop = asyncio.get_event_loop()
                info = await loop.run_in_executor(
                    None, lambda: ydl.extract_info(f'https://www.youtube.com/watch?v={video_id}', download=False)
                )
                return {
                    'title': info.get('title', 'Unknown Title'),
                    'duration': info.get('duration', 0),
                    'description': info.get('description', ''),
                    'uploader': info.get('uploader', ''),
                    'upload_date': info.get('upload_date', ''),
                    'view_count': info.get('view_count', 0)
                }

        except Exception as e:
            logger.error(f"Error getting video info for {video_id}: {e}", exc_info=True)
            try:
                logger.info(f"Attempting fallback extraction for {video_id}")
                fallback_opts = _get_ydl_opts(cookie_file_path, {
                    'skip_download': True,
                    'extractor_args': { 'youtube': { 'player_client': ['android', 'web'], } }
                })

                with YoutubeDL(fallback_opts) as ydl:
                    loop = asyncio.get_event_loop()
                    info = await loop.run_in_executor(
                        None, lambda: ydl.extract_info(f'https://www.youtube.com/watch?v={video_id}', download=False)
                    )
                    logger.info(f"Fallback extraction successful for {video_id}")
                    return {
                        'title': info.get('title', 'Unknown Title'),
                        'duration': info.get('duration', 0),
                        'description': info.get('description', ''),
                        'uploader': info.get('uploader', ''),
                        'upload_date': info.get('upload_date', ''),
                        'view_count': info.get('view_count', 0)
                    }
            except Exception as fallback_e:
                logger.error(f"Fallback also failed for {video_id}: {fallback_e}")

            return {
                'title': f'YouTube Video {video_id}',
                'duration': 0,
                'description': 'Video may be geo-blocked or unavailable',
                'uploader': '',
                'upload_date': '',
                'view_count': 0
            }


async def get_video_info_and_transcript(video_id: str, video_db_id: int) -> Dict:
    """Get video info and transcript using yt-dlp, which is more robust against IP blocks."""
    download_dir = os.path.join(settings.UPLOAD_DIR, 'youtube')
    os.makedirs(download_dir, exist_ok=True)
    output_path_template = os.path.join(download_dir, f'video_{video_db_id}')
    
    extra_ydl_opts = {
        'outtmpl': f'{output_path_template}.%(ext)s',
        'dumpjson': True,
        'writeautomaticsub': True,
        'subtitleslangs': ['en'],
        'subtitlesformat': 'json3',
        'skip_download': True
    }
    
    with _get_cookie_file_path() as cookie_file_path:
        ydl_opts = _get_ydl_opts(cookie_file_path, extra_ydl_opts)

        try:
            with YoutubeDL(ydl_opts) as ydl:
                loop = asyncio.get_event_loop()
                video_info = await loop.run_in_executor(
                    None, lambda: ydl.extract_info(f'https://www.youtube.com/watch?v={video_id}', download=False)
                )

                transcript_path = f"{output_path_template}.en.json3"
                transcript_text = ""
                transcript_data = []

                if os.path.exists(transcript_path):
                    with open(transcript_path, 'r', encoding='utf-8') as f:
                        raw_transcript = json.load(f)
                    for event in raw_transcript.get('events', []):
                        if 'segs' in event:
                            text = "".join([seg['utf8'] for seg in event['segs']]).strip()
                            if text:
                                start_time = event.get('tStartMs', 0) / 1000
                                transcript_text += text + " "
                                transcript_data.append({
                                    "text": text,
                                    "start": start_time,
                                    "duration": event.get('dDurationMs', 1000) / 1000
                                })
                    os.remove(transcript_path)
                
                return {
                    "id": video_id,
                    "title": video_info.get('title', f'YouTube Video {video_id}'),
                    "duration": video_info.get('duration', 0),
                    "description": video_info.get('description', ''),
                    "thumbnail": video_info.get('thumbnail', f"https://img.youtube.com/vi/{video_id}/maxresdefault.jpg"),
                    "uploader": video_info.get('uploader', 'Unknown'),
                    "view_count": video_info.get('view_count', 0),
                    "transcript_text": transcript_text.strip(),
                    "transcript_data": transcript_data
                }
        except Exception as e:
            logger.error(f"yt-dlp failed to get info/transcript for {video_id}: {e}", exc_info=True)
            return {
                "id": video_id,
                "title": f"YouTube Video {video_id}",
                "duration": None,
                "description": "",
                "thumbnail": f"https://img.youtube.com/vi/{video_id}/maxresdefault.jpg",
                "transcript_text": "",
                "transcript_data": []
            }
    

async def download_video(video_id: str, video_db_id: int) -> Optional[str]:
    """Download YouTube video using yt-dlp"""
    download_dir = os.path.join(settings.UPLOAD_DIR, 'youtube')
    os.makedirs(download_dir, exist_ok=True)
    output_path = os.path.join(download_dir, f'video_{video_db_id}.mp4')
    
    with _get_cookie_file_path() as cookie_file_path:
        ydl_opts = _get_ydl_opts(cookie_file_path, {'outtmpl': output_path})
        
        try:
            with YoutubeDL(ydl_opts) as ydl:
                loop = asyncio.get_event_loop()
                await loop.run_in_executor(
                    None, lambda: ydl.download([f'https://www.youtube.com/watch?v={video_id}'])
                )

            if os.path.exists(output_path):
                logger.info(f"Successfully downloaded video to {output_path}")
                return output_path
            else:
                logger.error(f"yt-dlp download completed but file not found at {output_path}")
                return None
                
        except Exception as e:
            logger.error(f"Error downloading YouTube video {video_id} with yt-dlp: {e}", exc_info=True)
            return None
