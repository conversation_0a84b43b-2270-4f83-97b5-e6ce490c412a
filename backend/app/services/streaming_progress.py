"""
Streaming Progress Service for Real-time Search Updates
Fixes TTFT pain by providing immediate feedback to users
"""

import asyncio
import json
import time
from typing import Dict, List, Optional, AsyncGenerator
from dataclasses import dataclass, asdict
from enum import Enum
import logging

logger = logging.getLogger(__name__)

class SearchStage(Enum):
    INITIALIZING = "initializing"
    EXTRACTING_FRAMES = "extracting_frames"
    ANALYZING_FRAMES = "analyzing_frames"
    PROCESSING_RESULTS = "processing_results"
    COMPLETED = "completed"
    ERROR = "error"

@dataclass
class ProgressUpdate:
    """Progress update data structure"""
    search_id: str
    stage: SearchStage
    progress_percent: float
    frames_processed: int
    total_frames: int
    matches_found: int
    current_message: str
    elapsed_time: float
    estimated_remaining: Optional[float] = None
    partial_results: Optional[List[Dict]] = None

class StreamingProgressManager:
    """Manages streaming progress updates for video search operations"""
    
    def __init__(self):
        self.active_searches: Dict[str, Dict] = {}
        self.subscribers: Dict[str, List[asyncio.Queue]] = {}
        
    async def start_search(self, search_id: str, total_frames: int) -> None:
        """Initialize a new search session"""
        self.active_searches[search_id] = {
            'start_time': time.time(),
            'total_frames': total_frames,
            'frames_processed': 0,
            'matches_found': 0,
            'stage': SearchStage.INITIALIZING,
            'partial_results': []
        }
        self.subscribers[search_id] = []
        
        # Send initial update
        await self._broadcast_update(search_id, "Search initialized")
        
    async def update_progress(self, 
                            search_id: str, 
                            stage: SearchStage,
                            frames_processed: int = None,
                            matches_found: int = None,
                            message: str = None,
                            partial_result: Dict = None) -> None:
        """Update search progress and broadcast to subscribers"""
        
        if search_id not in self.active_searches:
            logger.warning(f"Search {search_id} not found in active searches")
            return
            
        search_data = self.active_searches[search_id]
        
        # Update search data
        search_data['stage'] = stage
        if frames_processed is not None:
            search_data['frames_processed'] = frames_processed
        if matches_found is not None:
            search_data['matches_found'] = matches_found
        if partial_result:
            search_data['partial_results'].append(partial_result)
            
        # Broadcast update
        await self._broadcast_update(search_id, message or f"Stage: {stage.value}")
        
    async def add_partial_result(self, search_id: str, result: Dict) -> None:
        """Add a partial result and broadcast immediately"""
        if search_id not in self.active_searches:
            return
            
        search_data = self.active_searches[search_id]
        search_data['partial_results'].append(result)
        search_data['matches_found'] = len(search_data['partial_results'])
        
        await self._broadcast_update(search_id, f"Found match at {result.get('timestamp', 0):.1f}s")
        
    async def complete_search(self, search_id: str, final_results: List[Dict]) -> None:
        """Mark search as completed"""
        if search_id not in self.active_searches:
            return
            
        search_data = self.active_searches[search_id]
        search_data['stage'] = SearchStage.COMPLETED
        search_data['partial_results'] = final_results
        search_data['matches_found'] = len(final_results)
        
        await self._broadcast_update(search_id, f"Search completed - {len(final_results)} matches found")
        
        # Clean up after 30 seconds
        asyncio.create_task(self._cleanup_search(search_id, delay=30))
        
    async def error_search(self, search_id: str, error_message: str) -> None:
        """Mark search as errored"""
        if search_id not in self.active_searches:
            return
            
        search_data = self.active_searches[search_id]
        search_data['stage'] = SearchStage.ERROR
        
        await self._broadcast_update(search_id, f"Error: {error_message}")
        
        # Clean up after 10 seconds
        asyncio.create_task(self._cleanup_search(search_id, delay=10))
        
    async def subscribe(self, search_id: str) -> AsyncGenerator[str, None]:
        """Subscribe to progress updates for a search"""
        if search_id not in self.subscribers:
            self.subscribers[search_id] = []
            
        queue = asyncio.Queue()
        self.subscribers[search_id].append(queue)
        
        try:
            # Send current state if search exists
            if search_id in self.active_searches:
                current_update = self._create_progress_update(search_id, "Connected to search progress")
                yield f"data: {json.dumps(asdict(current_update))}\n\n"
            
            # Stream updates
            while True:
                try:
                    update_data = await asyncio.wait_for(queue.get(), timeout=30.0)
                    yield f"data: {json.dumps(update_data)}\n\n"
                    
                    # Break if search is completed or errored
                    if update_data.get('stage') in [SearchStage.COMPLETED.value, SearchStage.ERROR.value]:
                        break
                        
                except asyncio.TimeoutError:
                    # Send keepalive
                    yield f"data: {json.dumps({'type': 'keepalive', 'timestamp': time.time()})}\n\n"
                    
        except Exception as e:
            logger.error(f"Error in progress subscription: {e}")
        finally:
            # Clean up subscription
            if search_id in self.subscribers and queue in self.subscribers[search_id]:
                self.subscribers[search_id].remove(queue)
                
    async def _broadcast_update(self, search_id: str, message: str) -> None:
        """Broadcast update to all subscribers"""
        if search_id not in self.subscribers:
            return
            
        update = self._create_progress_update(search_id, message)
        update_data = asdict(update)
        
        # Send to all subscribers
        for queue in self.subscribers[search_id]:
            try:
                await queue.put(update_data)
            except Exception as e:
                logger.error(f"Error broadcasting update: {e}")
                
    def _create_progress_update(self, search_id: str, message: str) -> ProgressUpdate:
        """Create a progress update object"""
        search_data = self.active_searches.get(search_id, {})
        
        elapsed_time = time.time() - search_data.get('start_time', time.time())
        frames_processed = search_data.get('frames_processed', 0)
        total_frames = search_data.get('total_frames', 1)
        
        progress_percent = (frames_processed / total_frames) * 100 if total_frames > 0 else 0
        
        # Estimate remaining time
        estimated_remaining = None
        if frames_processed > 0 and progress_percent < 100:
            time_per_frame = elapsed_time / frames_processed
            remaining_frames = total_frames - frames_processed
            estimated_remaining = time_per_frame * remaining_frames
            
        return ProgressUpdate(
            search_id=search_id,
            stage=search_data.get('stage', SearchStage.INITIALIZING),
            progress_percent=progress_percent,
            frames_processed=frames_processed,
            total_frames=total_frames,
            matches_found=search_data.get('matches_found', 0),
            current_message=message,
            elapsed_time=elapsed_time,
            estimated_remaining=estimated_remaining,
            partial_results=(search_data.get('partial_results', []) or [])[-5:]  # Last 5 results
        )
        
    async def _cleanup_search(self, search_id: str, delay: int = 30) -> None:
        """Clean up search data after delay"""
        await asyncio.sleep(delay)
        
        if search_id in self.active_searches:
            del self.active_searches[search_id]
        if search_id in self.subscribers:
            del self.subscribers[search_id]
            
        logger.info(f"Cleaned up search {search_id}")

# Global instance
progress_manager = StreamingProgressManager()
