"""
Background Video Indexer Worker
Auto-launches on upload for sub-second search performance
"""

import asyncio
import logging
import time
from typing import Dict, Optional

from ..core.config import settings
from ..services.fast_frame_extractor import fast_extractor, FFMPEG_AVAILABLE
from ..services.clip_indexer import clip_indexer
from ..services.redis_cache import redis_cache

logger = logging.getLogger(__name__)

class VideoIndexerWorker:
    """Background worker for video indexing"""
    
    def __init__(self):
        self.active_jobs = {}
        
    async def index_video(self, video_id: int, video_path: str) -> Dict:
        """
        Main indexing task - runs in background after upload
        
        Args:
            video_id: Video ID
            video_path: Path to video file
            
        Returns:
            Indexing result status
        """
        
        job_id = f"index_{video_id}_{int(time.time())}"
        
        logger.info(f"Starting background indexing job {job_id} for video {video_id}")
        
        # Track job
        self.active_jobs[job_id] = {
            'video_id': video_id,
            'status': 'starting',
            'start_time': time.time(),
            'stage': 'initialization'
        }
        
        try:
            # Update status
            await self._update_job_status(job_id, 'running', 'frame_extraction')
            
            # Step 1: Fast frame extraction with FFmpeg
            if FFMPEG_AVAILABLE:
                frames_data = await fast_extractor.extract_frames_fast(video_path, video_id)
            else:
                logger.warning("FFmpeg not available - falling back to OpenCV")
                frames_data = await self._fallback_frame_extraction(video_path, video_id)
            
            if not frames_data:
                await self._update_job_status(job_id, 'failed', 'frame_extraction', 
                                            error="No frames extracted")
                return self.active_jobs[job_id]
            
            logger.info(f"Extracted {len(frames_data)} frames for video {video_id}")
            
            # Step 2: CLIP indexing
            await self._update_job_status(job_id, 'running', 'clip_indexing')
            
            if clip_indexer.is_available():
                success = await clip_indexer.index_video_frames(video_id, frames_data)
                
                if success:
                    logger.info(f"CLIP indexing completed for video {video_id}")
                    
                    # Update cache with index status
                    await redis_cache.set_video_index_status(video_id, {
                        'indexed': True,
                        'clip_available': True,
                        'frame_count': len(frames_data),
                        'indexed_at': time.time()
                    })
                else:
                    logger.warning(f"CLIP indexing failed for video {video_id}")
            else:
                logger.warning("CLIP not available - skipping vector indexing")
            
            # Step 3: Gemini upload for native search
            await self._update_job_status(job_id, 'running', 'gemini_upload')

            upload_result = await self._upload_to_gemini(video_path, video_id)
            if upload_result:
                logger.info(f"Gemini upload completed for video {video_id}")
            else:
                logger.warning(f"Gemini upload failed for video {video_id}")

            # Step 4: Complete
            await self._update_job_status(job_id, 'completed', 'complete')
            
            total_time = time.time() - self.active_jobs[job_id]['start_time']
            logger.info(f"Background indexing completed for video {video_id} in {total_time:.2f}s")
            
            return self.active_jobs[job_id]
            
        except Exception as e:
            logger.error(f"Background indexing failed for video {video_id}: {e}")
            await self._update_job_status(job_id, 'failed', 'error', error=str(e))
            return self.active_jobs[job_id]
        
        finally:
            # Cleanup job after delay
            asyncio.create_task(self._cleanup_job(job_id, delay=300))  # 5 minutes
    
    async def _update_job_status(self, job_id: str, status: str, stage: str, error: str = None):
        """Update job status"""
        
        if job_id in self.active_jobs:
            self.active_jobs[job_id].update({
                'status': status,
                'stage': stage,
                'updated_at': time.time()
            })
            
            if error:
                self.active_jobs[job_id]['error'] = error
            
            logger.debug(f"Job {job_id} status: {status} - {stage}")
    
    async def _fallback_frame_extraction(self, video_path: str, video_id: int) -> list:
        """Fallback frame extraction using OpenCV"""
        
        import cv2
        import os
        
        frames_data = []
        
        try:
            cap = cv2.VideoCapture(video_path)
            if not cap.isOpened():
                return []
            
            fps = cap.get(cv2.CAP_PROP_FPS)
            total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            
            # Sample every N seconds
            frame_interval = int(fps * settings.FRAME_SAMPLING_INTERVAL)
            
            # Create output directory
            out_dir = os.path.join(settings.INDICES_DIR, f"frames_{video_id}")
            os.makedirs(out_dir, exist_ok=True)
            
            frame_count = 0
            for frame_idx in range(0, total_frames, frame_interval):
                cap.set(cv2.CAP_PROP_POS_FRAMES, frame_idx)
                ret, frame = cap.read()
                
                if ret:
                    # Resize frame
                    height, width = frame.shape[:2]
                    if width > 512:
                        scale = 512 / width
                        new_width = 512
                        new_height = int(height * scale)
                        frame = cv2.resize(frame, (new_width, new_height))
                    
                    # Save frame
                    frame_path = os.path.join(out_dir, f"{frame_count:06d}.jpg")
                    cv2.imwrite(frame_path, frame, [cv2.IMWRITE_JPEG_QUALITY, 85])
                    
                    timestamp = frame_idx / fps
                    
                    frames_data.append({
                        'frame_path': frame_path,
                        'timestamp': timestamp,
                        'frame_index': frame_count,
                        'video_id': video_id,
                        'relative_path': f"frames_{video_id}/{frame_count:06d}.jpg"
                    })
                    
                    frame_count += 1
            
            cap.release()
            logger.info(f"OpenCV extracted {len(frames_data)} frames for video {video_id}")
            
        except Exception as e:
            logger.error(f"Fallback frame extraction failed: {e}")
            
        return frames_data

    async def _upload_to_gemini(self, video_path: str, video_id: int) -> bool:
        """Upload video to Gemini for native search capabilities"""

        try:
            # Import Gemini service
            from ..services.gemini_service import GeminiService

            # Initialize Gemini service
            gemini_service = GeminiService()

            # Upload video file to Gemini
            logger.info(f"Starting Gemini upload for video {video_id}")

            # Read video file
            with open(video_path, 'rb') as video_file:
                video_data = video_file.read()

            # Upload to Gemini (this would be the actual implementation)
            # For now, we'll simulate the upload process
            upload_response = await gemini_service.upload_video_file(
                video_data=video_data,
                video_id=video_id,
                filename=f"video_{video_id}.mp4"
            )

            if upload_response and upload_response.get('success'):
                # Store Gemini file URI for future native searches
                await redis_cache.set_video_index_status(video_id, {
                    'gemini_uploaded': True,
                    'gemini_file_uri': upload_response.get('file_uri'),
                    'uploaded_at': time.time()
                })

                logger.info(f"Gemini upload successful for video {video_id}")
                return True
            else:
                logger.error(f"Gemini upload failed for video {video_id}: {upload_response}")
                return False

        except Exception as e:
            logger.error(f"Error uploading video {video_id} to Gemini: {e}")
            return False

    async def _cleanup_job(self, job_id: str, delay: int = 300):
        """Clean up job after delay"""
        
        await asyncio.sleep(delay)
        
        if job_id in self.active_jobs:
            del self.active_jobs[job_id]
            logger.debug(f"Cleaned up job {job_id}")
    
    def get_job_status(self, job_id: str) -> Optional[Dict]:
        """Get job status"""
        return self.active_jobs.get(job_id)
    
    def get_active_jobs(self) -> Dict:
        """Get all active jobs"""
        return self.active_jobs.copy()

class AutoIndexer:
    """Auto-indexer that starts indexing on video upload"""
    
    def __init__(self):
        self.worker = VideoIndexerWorker()
        
    async def auto_index_video(self, video_id: int, video_path: str) -> str:
        """
        Auto-start indexing for uploaded video
        
        Args:
            video_id: Video ID
            video_path: Path to video file
            
        Returns:
            Job ID for tracking
        """
        
        if not settings.AUTO_INDEX_UPLOADS:
            logger.info("Auto-indexing disabled")
            return None
        
        logger.info(f"Auto-starting indexing for video {video_id}")
        
        # Start indexing in background
        job_id = f"index_{video_id}_{int(time.time())}"
        
        asyncio.create_task(
            self.worker.index_video(video_id, video_path)
        )
        
        return job_id
    
    def get_indexing_status(self, video_id: int) -> Optional[Dict]:
        """Get indexing status for a video"""
        
        # Look for active job
        for job_id, job_data in self.worker.active_jobs.items():
            if job_data.get('video_id') == video_id:
                return job_data
        
        return None

# Global instances
video_indexer = VideoIndexerWorker()
auto_indexer = AutoIndexer()

async def start_auto_indexing(video_id: int, video_path: str) -> Optional[str]:
    """
    Convenience function to start auto-indexing
    
    Args:
        video_id: Video ID
        video_path: Path to video file
        
    Returns:
        Job ID if started, None if disabled
    """
    
    return await auto_indexer.auto_index_video(video_id, video_path)
