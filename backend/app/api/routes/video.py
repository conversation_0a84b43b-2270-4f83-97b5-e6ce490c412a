from fastapi import APIRouter, HTTPException, Depends, Request, BackgroundTasks, UploadFile, File
from sqlalchemy.orm import Session
from pydantic import BaseModel
import logging
import asyncio
import os
import shutil
from typing import Dict

from app.core.database import get_db
from app.core.config import settings
from app.models.video import Video, VideoFrame
from app.services import youtube_service as yt_service
# Enhanced services (require additional dependencies)
try:
    from app.services.youtube_optimizer import YouTubeOptimizer
    ENHANCED_PROCESSING_AVAILABLE = True
except ImportError:
    ENHANCED_PROCESSING_AVAILABLE = False

# Auto-indexing for fast path
try:
    from app.tasks.indexer import start_auto_indexing
    AUTO_INDEXING_AVAILABLE = True
except ImportError:
    AUTO_INDEXING_AVAILABLE = False
# Frame analysis functionality removed for stability

logger = logging.getLogger(__name__)
router = APIRouter()

# In-memory storage for processing status (in production, use Redis or similar)
processing_status: Dict[int, Dict] = {}

async def process_youtube_video_background(
    video_id: int,
    youtube_id: str,
    enable_visual_search: bool = True
):
    """Background task to process YouTube video using robust yt-dlp service"""
    from app.core.database import SessionLocal
    db = SessionLocal()
    
    try:
        video = db.query(Video).filter(Video.id == video_id).first()
        if not video:
            logger.error(f"Video {video_id} not found in database for background processing")
            return

        # Update status
        processing_status[video_id] = {"status": "processing", "progress": 10, "message": "Fetching video info and transcript...", "stage": "fetching"}
        
        # Use the new robust method to get info and transcript together
        video_data = await yt_service.get_video_info_and_transcript(youtube_id, video.id)
        
        if not video_data.get("transcript_text"):
            logger.warning(f"Could not retrieve transcript for {youtube_id}. Analysis will be limited.")
            video.status = "failed"
            video.transcript = "Transcript not available."
            processing_status[video_id] = {"status": "failed", "progress": 0, "message": "Failed to retrieve transcript.", "stage": "error"}
            db.commit()
            return

        # Update video record with transcript and metadata
        video.transcript = video_data["transcript_text"]
        # In a real app, you would use Gemini to generate sections from the transcript here
        video.sections = [] 
        video.duration = video_data.get("duration", 0)
        video.analysis_metadata = {
            "analysis_method": "yt-dlp_robust",
            "uploader": video_data.get("uploader"),
            "view_count": video_data.get("view_count")
        }
        db.commit()

        processing_status[video_id] = {"status": "processing", "progress": 40, "message": "Transcript processed, starting video download...", "stage": "downloading"}

        # Now, download the video file for frame analysis
        video_path = await yt_service.download_video(youtube_id, video.id)
        
        if video_path and os.path.exists(video_path):
            video.file_path = video_path
            video.status = "downloaded"
            db.commit()

            processing_status[video_id] = {"status": "processing", "progress": 70, "message": "Video downloaded, starting auto-indexing...", "stage": "indexing"}
            
            # Start auto-indexing for fast visual search
            if AUTO_INDEXING_AVAILABLE and enable_visual_search:
                logger.info(f"Starting auto-indexing for video {video.id}")
                await start_auto_indexing(video.id, video_path)
                processing_status[video_id]["message"] = "Auto-indexing complete."
            
            video.status = "completed"
            processing_status[video_id] = {"status": "completed", "progress": 100, "message": "Video processing and indexing complete.", "stage": "complete"}
        else:
            logger.info(f"Video download blocked for {youtube_id} (common on cloud platforms), but transcript was successfully processed.")
            video.status = "completed_transcript_only"
            processing_status[video_id] = {
                "status": "completed",
                "progress": 100,
                "message": "✅ Video processed successfully! Chat functionality is available. (Visual search unavailable due to cloud platform restrictions)",
                "stage": "complete"
            }

        db.commit()

    except Exception as e:
        logger.error(f"Full background processing for video {video_id} failed: {e}", exc_info=True)
        if 'video' in locals() and db.is_active:
            video.status = "failed"
            db.commit()
        processing_status[video_id] = {"status": "failed", "progress": 0, "message": f"An unexpected error occurred: {e}", "stage": "error"}
    finally:
        db.close()


class YouTubeRequest(BaseModel):
    youtube_url: str
    enable_visual_search: bool = True

@router.post("/youtube")
async def process_youtube_video(
    request: Request,
    youtube_request: YouTubeRequest,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    """Process a YouTube video - returns immediately and processes in background"""
    try:
        # Extract video ID
        video_id = yt_service.extract_video_id(youtube_request.youtube_url)
        if not video_id:
            raise HTTPException(status_code=400, detail="Invalid YouTube URL")

        # Get basic video info (quick operation)
        video_info = await yt_service.get_video_info(video_id)

        # Create video record immediately
        video = Video(
            title=video_info.get('title', 'Untitled YouTube Video'),
            video_type='youtube',
            url=youtube_request.youtube_url,
            youtube_id=video_id,
            status='processing',
            duration=video_info.get('duration', 0)
        )
        db.add(video)
        db.commit()
        db.refresh(video)

        # Initialize processing status
        processing_status[video.id] = {
            "status": "processing",
            "progress": 0,
            "message": "Starting YouTube video processing...",
            "stage": "initialization"
        }

        # Start background processing
        background_tasks.add_task(
            process_youtube_video_background,
            video_id=video.id,
            youtube_id=video_id,
            enable_visual_search=youtube_request.enable_visual_search
        )

        # Return immediately with video ID
        return {
            "video_id": video.id,
            "status": "processing",
            "message": "YouTube video processing started",
            "title": video.title,
            "duration": video.duration,
            "youtube_id": video_id
        }

    except Exception as e:
        logger.error(f"Error initiating YouTube video processing: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/upload")
async def upload_video(
    request: Request,
    file: UploadFile = File(...),
    db: Session = Depends(get_db)
):
    """Upload a video file for processing"""
    try:
        # Validate file type
        if not file.content_type or not file.content_type.startswith('video/'):
            raise HTTPException(status_code=400, detail="File must be a video")

        # Create upload directory (use same as YouTube videos for consistency)
        upload_dir = os.path.join(settings.UPLOAD_DIR, 'youtube')
        os.makedirs(upload_dir, exist_ok=True)

        # Generate unique filename
        file_extension = os.path.splitext(file.filename)[1] if file.filename else '.mp4'
        filename = f"upload_{int(asyncio.get_event_loop().time())}_{file.filename}"
        file_path = os.path.join(upload_dir, filename)

        # Save uploaded file
        with open(file_path, "wb") as buffer:
            shutil.copyfileobj(file.file, buffer)

        # Create video record
        video = Video(
            title=file.filename or "Uploaded Video",
            video_type='upload',
            url=file_path,
            file_path=file_path,
            status='completed'
        )
        db.add(video)
        db.commit()
        db.refresh(video)

        # Process video for frame extraction in background
        from app.services.video_processor import VideoProcessor
        processor = VideoProcessor()

        try:
            result = await processor.process_uploaded_video(file_path, video.id)
            if result["status"] == "success":
                video.frame_count = result.get("frame_count", 0)
                video.duration = result.get("video_info", {}).get("duration", 0)
                db.commit()
        except Exception as process_error:
            logger.warning(f"Frame processing failed for uploaded video {video.id}: {process_error}")

        return {
            "video_id": video.id,
            "status": "completed",
            "message": "Video uploaded and processed successfully",
            "title": video.title,
            "duration": video.duration
        }

    except Exception as e:
        logger.error(f"Error uploading video: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/{video_id}/status")
async def get_video_status(video_id: int, db: Session = Depends(get_db)):
    """Get the current processing status of a video"""
    # Check if we have processing status
    if video_id in processing_status:
        status_info = processing_status[video_id].copy()
        
        # Get video from database for additional info
        video = db.query(Video).filter(Video.id == video_id).first()
        if video:
            status_info.update({
                "video_id": video.id,
                "title": video.title,
                "video_type": video.video_type,
                "duration": video.duration,
                "frame_count": video.frame_count,
                "has_transcript": bool(video.transcript),
                "sections_count": len(video.sections) if video.sections else 0
            })
        
        # Clean up completed/failed statuses after returning
        if status_info["status"] in ["completed", "failed"]:
            # Keep status for 5 minutes after completion
            asyncio.create_task(cleanup_status_after_delay(video_id, 300))
        
        return status_info
    
    # If no processing status, check database
    video = db.query(Video).filter(Video.id == video_id).first()
    if not video:
        raise HTTPException(status_code=404, detail="Video not found")
    
    # Return database status
    return {
        "video_id": video.id,
        "status": video.status,
        "progress": 100 if video.status == "completed" else 0,
        "message": f"Video is {video.status}",
        "stage": "complete" if video.status == "completed" else "unknown",
        "title": video.title,
        "video_type": video.video_type,
        "duration": video.duration,
        "frame_count": video.frame_count,
        "has_transcript": bool(video.transcript),
        "sections_count": len(video.sections) if video.sections else 0
    }

async def cleanup_status_after_delay(video_id: int, delay: int):
    """Remove processing status after a delay"""
    await asyncio.sleep(delay)
    if video_id in processing_status:
        del processing_status[video_id]

@router.get("/{video_id}")
async def get_video(video_id: int, db: Session = Depends(get_db)):
    """Get video information"""
    video = db.query(Video).filter(Video.id == video_id).first()
    if not video:
        raise HTTPException(status_code=404, detail="Video not found")

    # Prepare video URLs
    video_urls = {
        "original_url": video.url,  # Original YouTube URL
        "local_url": None,  # Local video file URL
        "youtube_id": video.youtube_id
    }

    # Add local video URL if file exists
    if video.file_path and os.path.exists(video.file_path):
        # Convert file path to URL path
        filename = os.path.basename(video.file_path)
        video_urls["local_url"] = f"/api/videos/{filename}"

    return {
        "id": video.id,
        "title": video.title,
        "url": video.url,  # Keep for backward compatibility
        "video_urls": video_urls,  # New structured URLs
        "video_type": video.video_type,
        "status": video.status,
        "duration": video.duration,
        "has_transcript": bool(video.transcript),
        "sections": video.sections,
        "frame_count": video.frame_count,
        "created_at": video.created_at
    }

@router.get("/{video_id}/sections")
async def get_video_sections(video_id: int, db: Session = Depends(get_db)):
    """Get video sections"""
    video = db.query(Video).filter(Video.id == video_id).first()
    if not video:
        raise HTTPException(status_code=404, detail="Video not found")

    return {
        "video_id": video.id,
        "sections": video.sections or []
    }


@router.post("/{video_id}/sections/generate")
async def generate_video_sections(video_id: int, request: Request, db: Session = Depends(get_db)):
    """Generate sections for a video that doesn't have them"""
    video = db.query(Video).filter(Video.id == video_id).first()
    if not video:
        raise HTTPException(status_code=404, detail="Video not found")

    try:
        # Get gemini service from app state
        gemini_service = request.app.state.gemini_service

        # Generate sections using Gemini
        video_info = {
            "title": video.title,
            "duration": video.duration,
            "video_type": video.video_type
        }

        # Use transcript if available, otherwise use video analysis
        transcript = video.transcript or "No transcript available for this video."

        sections = await gemini_service.generate_video_sections(transcript, video_info)

        if sections:
            # Update video with generated sections
            video.sections = sections
            db.commit()

            logger.info(f"Generated {len(sections)} sections for video {video_id}")
            return {
                "video_id": video_id,
                "sections": sections,
                "message": f"Generated {len(sections)} sections successfully"
            }
        else:
            return {
                "video_id": video_id,
                "sections": [],
                "message": "Failed to generate sections"
            }

    except Exception as e:
        logger.error(f"Error generating sections for video {video_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to generate sections: {str(e)}")


@router.get("/")
async def list_videos(db: Session = Depends(get_db)):
    """Get list of all videos"""
    videos = db.query(Video).order_by(Video.created_at.desc()).all()

    video_list = []
    for video in videos:
        # Prepare video URLs
        video_urls = {
            "original_url": video.url,
            "local_url": None,
            "youtube_id": video.youtube_id
        }

        # Add local video URL if file exists
        if video.file_path and os.path.exists(video.file_path):
            filename = os.path.basename(video.file_path)
            video_urls["local_url"] = f"/api/videos/{filename}"

        video_list.append({
            "id": video.id,
            "title": video.title,
            "url": video.url,
            "video_urls": video_urls,
            "video_type": video.video_type,
            "status": video.status,
            "duration": video.duration,
            "has_transcript": bool(video.transcript),
            "frame_count": video.frame_count,
            "created_at": video.created_at
        })

    return {"videos": video_list}

@router.delete("/{video_id}")
async def delete_video(
    request: Request,
    video_id: int,
    db: Session = Depends(get_db)
):
    """Delete a video and its associated files"""
    video = db.query(Video).filter(Video.id == video_id).first()
    if not video:
        raise HTTPException(status_code=404, detail="Video not found")

    try:
        # Clean up files
        video_processor = request.app.state.video_processor
        video_processor.cleanup_video_files(video_id)

        # Delete from database
        db.query(VideoFrame).filter(VideoFrame.video_id == video_id).delete()
        db.delete(video)
        db.commit()

        return {"message": "Video deleted successfully"}

    except Exception as e:
        logger.error(f"Error deleting video {video_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


