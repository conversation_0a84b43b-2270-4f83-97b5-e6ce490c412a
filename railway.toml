[build]
builder = "DOCKERFILE"
dockerfilePath = "backend/Dockerfile"

[deploy]
startCommand = "python main.py"
healthcheckPath = "/health"
healthcheckTimeout = 300
restartPolicyType = "ON_FAILURE"
restartPolicyMaxRetries = 10

[env]
PORT = { default = "8002" }
HOST = { default = "0.0.0.0" }
DEBUG = { default = "false" }
PYTHONPATH = { default = "/app" }

# These will be set in Railway dashboard
# GEMINI_API_KEY = "${{secrets.GEMINI_API_KEY}}"
# DATABASE_URL = "${{Postgres.DATABASE_URL}}"
# REDIS_URL = "${{Redis.REDIS_URL}}"
