# 🚀 VideoChat AI - Production Environment
# Update these values for your production deployment

# =============================================================================
# REQUIRED: API Keys
# =============================================================================
GEMINI_API_KEY=your_actual_gemini_api_key_here

# =============================================================================
# Database Configuration
# =============================================================================
# For Railway: Use ${{Postgres.DATABASE_URL}}
# For AWS: Use your RDS connection string
DATABASE_URL=postgresql://user:password@host:port/database

# =============================================================================
# Server Configuration
# =============================================================================
HOST=0.0.0.0
PORT=8002
DEBUG=false

# =============================================================================
# CORS Configuration (CRITICAL!)
# =============================================================================
# Add your actual frontend domain(s) here
CORS_ORIGINS=https://your-app.vercel.app,https://your-domain.com

# Performance Settings
MAX_VIDEO_SIZE_MB=500
FRAME_EXTRACTION_INTERVAL=5
MAX_VIDEO_DURATION_SECONDS=3600
MAX_FRAMES_PER_VIDEO=300

# Optional: Redis for better performance
REDIS_URL=redis://localhost:6379/0
CACHE_TTL=3600

# Optional: Advanced video search
AUTO_INDEX_UPLOADS=true
FRAME_SAMPLING_INTERVAL=5
TARGET_SEARCH_LATENCY_MS=20
