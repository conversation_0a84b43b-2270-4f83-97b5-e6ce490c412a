#!/bin/bash

# Fast Search Dependencies Installation Script
# Installs CLIP, Faiss, and other dependencies for sub-2s video search

echo "🚀 Installing Fast Search Dependencies for Sub-2s Video Search"
echo "=============================================================="

# Check if we're in the backend directory
if [ ! -f "requirements.txt" ]; then
    echo "❌ Error: requirements.txt not found. Please run this script from the backend directory."
    exit 1
fi

# Check Python version
python_version=$(python3 -c "import sys; print(f'{sys.version_info.major}.{sys.version_info.minor}')")
echo "📋 Python version: $python_version"

if [[ "$python_version" < "3.8" ]]; then
    echo "❌ Error: Python 3.8+ required for PyTorch and CLIP"
    exit 1
fi

# Check if FFmpeg is installed
if command -v ffmpeg &> /dev/null; then
    echo "✅ FFmpeg is available"
    ffmpeg_version=$(ffmpeg -version | head -n1)
    echo "   $ffmpeg_version"
else
    echo "⚠️  FFmpeg not found - frame extraction will be slower"
    echo "   Install FFmpeg for optimal performance:"
    echo "   - macOS: brew install ffmpeg"
    echo "   - Ubuntu: sudo apt install ffmpeg"
    echo "   - Windows: Download from https://ffmpeg.org/"
fi

# Check if Redis is running
if command -v redis-cli &> /dev/null; then
    if redis-cli ping &> /dev/null; then
        echo "✅ Redis is running"
    else
        echo "⚠️  Redis not running - caching will be disabled"
        echo "   Start Redis: redis-server"
    fi
else
    echo "⚠️  Redis not installed - caching will be disabled"
    echo "   Install Redis:"
    echo "   - macOS: brew install redis"
    echo "   - Ubuntu: sudo apt install redis-server"
fi

echo ""
echo "📦 Installing Python dependencies..."

# Install PyTorch first (CPU version for compatibility)
echo "Installing PyTorch (CPU)..."
pip3 install torch torchvision --index-url https://download.pytorch.org/whl/cpu

# Install CLIP
echo "Installing OpenAI CLIP..."
pip3 install git+https://github.com/openai/CLIP.git

# Install Faiss
echo "Installing Faiss (CPU)..."
pip3 install faiss-cpu

# Install other dependencies
echo "Installing other dependencies..."
pip3 install Pillow>=9.0.0

# Install all requirements
echo "Installing remaining requirements..."
pip3 install -r requirements.txt

echo ""
echo "🧪 Testing installations..."

# Test PyTorch
python3 -c "import torch; print(f'✅ PyTorch {torch.__version__} installed')" 2>/dev/null || echo "❌ PyTorch installation failed"

# Test CLIP
python3 -c "import clip; print('✅ CLIP installed successfully')" 2>/dev/null || echo "❌ CLIP installation failed"

# Test Faiss
python3 -c "import faiss; print(f'✅ Faiss installed successfully')" 2>/dev/null || echo "❌ Faiss installation failed"

# Test PIL
python3 -c "from PIL import Image; print('✅ Pillow installed successfully')" 2>/dev/null || echo "❌ Pillow installation failed"

echo ""
echo "🔧 Creating indices directory..."
mkdir -p indices

echo ""
echo "⚡ Performance Configuration"
echo "=========================="
echo "Add these to your .env file for optimal performance:"
echo ""
echo "# Fast Search Configuration"
echo "AUTO_INDEX_UPLOADS=true"
echo "FRAME_SAMPLING_INTERVAL=5"
echo "CLIP_BATCH_SIZE=32"
echo "FAISS_SEARCH_K=200"
echo "REDIS_TTL_SECONDS=86400"
echo "TARGET_SEARCH_LATENCY_MS=20"
echo ""

echo "🎯 Expected Performance Improvements:"
echo "======================================"
echo "• Search latency: 30+ seconds → <2 seconds"
echo "• Cache hits: ~20ms response time"
echo "• CLIP search: ~200ms for 30-min video"
echo "• Auto-indexing: ~15 seconds after upload"
echo ""

echo "✅ Fast Search Dependencies Installation Complete!"
echo ""
echo "🚀 Next Steps:"
echo "1. Restart your backend server"
echo "2. Upload a YouTube video"
echo "3. Wait ~15 seconds for auto-indexing"
echo "4. Try visual search - should be <2 seconds!"
echo ""
echo "📊 Monitor performance at: /api/v1/streaming/stats"
