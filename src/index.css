@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Force black theme by default */
    --background: 0 0% 0%;
    --foreground: 0 0% 100%;

    --card: 0 0% 0%;
    --card-foreground: 0 0% 100%;

    --popover: 0 0% 0%;
    --popover-foreground: 0 0% 100%;

    --primary: 0 0% 100%;
    --primary-foreground: 0 0% 0%;

    --secondary: 0 0% 10%;
    --secondary-foreground: 0 0% 100%;

    --muted: 0 0% 10%;
    --muted-foreground: 0 0% 60%;

    --accent: 0 0% 10%;
    --accent-foreground: 0 0% 100%;

    --destructive: 0 0% 80%;
    --destructive-foreground: 0 0% 0%;

    --border: 0 0% 15%;
    --input: 0 0% 15%;
    --ring: 0 0% 100%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 5%;
    --sidebar-foreground: 0 0% 95%;
    --sidebar-primary: 0 0% 100%;
    --sidebar-primary-foreground: 0 0% 0%;
    --sidebar-accent: 0 0% 10%;
    --sidebar-accent-foreground: 0 0% 100%;
    --sidebar-border: 0 0% 15%;
    --sidebar-ring: 0 0% 100%;
  }

  .dark {
    --background: 0 0% 0%;
    --foreground: 0 0% 100%;

    --card: 0 0% 0%;
    --card-foreground: 0 0% 100%;

    --popover: 0 0% 0%;
    --popover-foreground: 0 0% 100%;

    --primary: 0 0% 100%;
    --primary-foreground: 0 0% 0%;

    --secondary: 0 0% 10%;
    --secondary-foreground: 0 0% 100%;

    --muted: 0 0% 10%;
    --muted-foreground: 0 0% 60%;

    --accent: 0 0% 10%;
    --accent-foreground: 0 0% 100%;

    --destructive: 0 0% 80%;
    --destructive-foreground: 0 0% 0%;

    --border: 0 0% 15%;
    --input: 0 0% 15%;
    --ring: 0 0% 100%;
    --sidebar-background: 0 0% 5%;
    --sidebar-foreground: 0 0% 95%;
    --sidebar-primary: 0 0% 100%;
    --sidebar-primary-foreground: 0 0% 0%;
    --sidebar-accent: 0 0% 10%;
    --sidebar-accent-foreground: 0 0% 100%;
    --sidebar-border: 0 0% 15%;
    --sidebar-ring: 0 0% 100%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  html {
    @apply dark;
    background-color: black !important;
  }

  body {
    @apply bg-background text-foreground;
    background-color: black !important;
    color: white !important;
  }

  /* Ensure all main containers are black */
  #root {
    background-color: black !important;
    color: white !important;
    min-height: 100vh;
  }
}

/* Custom scrollbar styles for visual search and sections */
@layer utilities {
  .scrollbar-thin {
    scrollbar-width: thin;
  }

  .scrollbar-thumb-gray-500\/50 {
    scrollbar-color: rgba(128, 128, 128, 0.5) transparent;
  }

  .scrollbar-track-transparent {
    scrollbar-color: rgba(128, 128, 128, 0.5) transparent;
  }

  /* Webkit scrollbar styles */
  .scrollbar-thin::-webkit-scrollbar {
    width: 8px;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    background: transparent;
    border-radius: 4px;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    background: rgba(128, 128, 128, 0.5);
    border-radius: 4px;
    border: 1px solid rgba(0, 0, 0, 0.1);
  }

  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background: rgba(128, 128, 128, 0.7);
  }

  .scrollbar-thin::-webkit-scrollbar-corner {
    background: transparent;
  }

  /* Chat input and message text rendering fixes */
  .chat-input {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
    white-space: pre-wrap !important;
    word-wrap: break-word !important;
    word-break: break-word;
    overflow-wrap: break-word;
    line-height: 1.5;
  }

  .chat-message {
    white-space: pre-wrap !important;
    word-wrap: break-word !important;
    word-break: break-word;
    overflow-wrap: break-word;
    line-height: 1.5;
  }

  /* Ensure proper text spacing in all inputs */
  input[type="text"], textarea {
    font-feature-settings: normal;
    font-variant-ligatures: normal;
    text-rendering: optimizeLegibility;
  }
}