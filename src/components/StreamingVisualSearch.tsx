import React, { useState, useEffect, useCallback } from 'react';
import { Search, Eye, Clock, Zap, Film, ChevronDown, ChevronUp, Users, Target, Play, Activity, CheckCircle, AlertCircle } from 'lucide-react';
import { SearchResult, ClipResult, apiService } from '@/services/api';
import { useToast } from '@/hooks/use-toast';

interface StreamingVisualSearchProps {
  videoId: number;
  videoUrl: string;
  onTimeJump: (time: number) => void;
}

interface ProgressUpdate {
  search_id: string;
  stage: string;
  progress_percent: number;
  frames_processed: number;
  total_frames: number;
  matches_found: number;
  current_message: string;
  elapsed_time: number;
  estimated_remaining?: number;
  partial_results?: Array<{
    timestamp: number;
    confidence: number;
    description: string;
  }>;
}

const StreamingVisualSearch: React.FC<StreamingVisualSearchProps> = ({ videoId, onTimeJump }) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [isSearching, setIsSearching] = useState(false);
  const [searchResults, setSearchResults] = useState<SearchResult[]>([]);
  const [searchClips, setSearchClips] = useState<ClipResult[]>([]);
  const [hasSearched, setHasSearched] = useState(false);
  const [viewMode, setViewMode] = useState<'clips' | 'frames' | 'timeline'>('clips');
  const [directAnswer, setDirectAnswer] = useState<string>('');
  
  // Streaming progress state
  const [progressUpdate, setProgressUpdate] = useState<ProgressUpdate | null>(null);
  const [partialResults, setPartialResults] = useState<SearchResult[]>([]);
  const [eventSource, setEventSource] = useState<EventSource | null>(null);
  
  const { toast } = useToast();

  // Clean up event source on unmount
  useEffect(() => {
    return () => {
      if (eventSource) {
        eventSource.close();
      }
    };
  }, [eventSource]);

  const handleStreamingSearch = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!searchQuery.trim()) return;
    
    setIsSearching(true);
    setHasSearched(true);
    setSearchResults([]);
    setSearchClips([]);
    setPartialResults([]);
    setProgressUpdate(null);
    setDirectAnswer('');
    
    try {
      // Start the streaming search
      const startResponse = await fetch('/api/v1/streaming/search/start', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          video_id: videoId,
          query: searchQuery,
          max_results: 10,
          enable_early_termination: true
        })
      });
      
      if (!startResponse.ok) {
        throw new Error('Failed to start streaming search');
      }
      
      const { search_id } = await startResponse.json();
      
      // Connect to progress stream
      const es = new EventSource(`/api/v1/streaming/search/${search_id}/progress`);
      setEventSource(es);
      
      es.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          
          if (data.type === 'keepalive') {
            return; // Ignore keepalive messages
          }
          
          setProgressUpdate(data);
          
          // Update partial results as they come in
          if (data.partial_results && data.partial_results.length > 0) {
            const newPartialResults = data.partial_results.map((result: any) => ({
              timestamp: result.timestamp,
              confidence: result.confidence,
              description: result.description,
              frame_path: `/api/v1/search/${videoId}/frame?timestamp=${result.timestamp}`,
              clip_start: Math.max(0, result.timestamp - 5),
              clip_end: result.timestamp + 10
            }));
            setPartialResults(newPartialResults);
          }
          
          // Check if search is completed
          if (data.stage === 'completed') {
            // Get final results
            handleSearchCompletion(search_id);
            es.close();
            setEventSource(null);
          } else if (data.stage === 'error') {
            throw new Error(data.current_message || 'Search failed');
          }
          
        } catch (error) {
          console.error('Error parsing progress update:', error);
        }
      };
      
      es.onerror = (error) => {
        console.error('EventSource error:', error);
        es.close();
        setEventSource(null);
        setIsSearching(false);
        
        toast({
          title: "Connection Error",
          description: "Lost connection to search progress. Falling back to standard search.",
          variant: "destructive",
        });
        
        // Fallback to regular search
        handleFallbackSearch();
      };
      
    } catch (error) {
      console.error('Streaming search error:', error);
      setIsSearching(false);
      
      toast({
        title: "Search Failed",
        description: error instanceof Error ? error.message : "Failed to start streaming search",
        variant: "destructive",
      });
      
      // Fallback to regular search
      handleFallbackSearch();
    }
  };
  
  const handleSearchCompletion = async (searchId: string) => {
    try {
      // Get final search status
      const statusResponse = await fetch(`/api/v1/streaming/search/${searchId}/status`);
      if (statusResponse.ok) {
        const statusData = await statusResponse.json();
        
        // Convert partial results to final results
        if (statusData.partial_results && statusData.partial_results.length > 0) {
          const finalResults = statusData.partial_results.map((result: any) => ({
            timestamp: result.timestamp,
            confidence: result.confidence,
            description: result.description,
            frame_path: `/api/v1/search/${videoId}/frame?timestamp=${result.timestamp}`,
            clip_start: Math.max(0, result.timestamp - 5),
            clip_end: result.timestamp + 10
          }));
          
          setSearchResults(finalResults);
          setDirectAnswer(`Found ${finalResults.length} instances of "${searchQuery}" in the video`);
          
          // Convert to clips
          const clips = finalResults.map((result: SearchResult) => ({
            start_time: result.clip_start,
            end_time: result.clip_end,
            confidence: result.confidence,
            description: result.description,
            thumbnail_url: result.frame_path
          }));
          setSearchClips(clips);
        }
      }
    } catch (error) {
      console.error('Error getting final results:', error);
    } finally {
      setIsSearching(false);
    }
  };
  
  const handleFallbackSearch = async () => {
    try {
      // Use the optimized search endpoint as fallback
      const response = await apiService.request('/search/visual/optimized', {
        method: 'POST',
        body: JSON.stringify({
          video_id: videoId,
          query: searchQuery,
          max_results: 10,
          enable_streaming: false,
          enable_early_termination: true
        })
      });
      
      setSearchResults(response.results || []);
      setSearchClips(response.clips || []);
      setDirectAnswer(response.direct_answer || '');
      
    } catch (error) {
      console.error('Fallback search error:', error);
      toast({
        title: "Search Failed",
        description: "Both streaming and fallback search failed",
        variant: "destructive",
      });
    } finally {
      setIsSearching(false);
    }
  };

  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const getStageIcon = (stage: string) => {
    switch (stage) {
      case 'initializing':
        return <Activity className="w-4 h-4 animate-pulse" />;
      case 'extracting_frames':
        return <Film className="w-4 h-4 animate-pulse" />;
      case 'analyzing_frames':
        return <Eye className="w-4 h-4 animate-pulse" />;
      case 'processing_results':
        return <Zap className="w-4 h-4 animate-pulse" />;
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'error':
        return <AlertCircle className="w-4 h-4 text-red-500" />;
      default:
        return <Activity className="w-4 h-4" />;
    }
  };

  const getStageLabel = (stage: string) => {
    switch (stage) {
      case 'initializing':
        return 'Initializing search...';
      case 'extracting_frames':
        return 'Extracting key frames...';
      case 'analyzing_frames':
        return 'Analyzing frames with AI...';
      case 'processing_results':
        return 'Processing results...';
      case 'completed':
        return 'Search completed!';
      case 'error':
        return 'Search failed';
      default:
        return 'Processing...';
    }
  };

  return (
    <div className="space-y-6">
      {/* Search Form */}
      <form onSubmit={handleStreamingSearch} className="space-y-4">
        <div className="relative">
          <input
            type="text"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            placeholder="Search for anything... (e.g., 'person', 'red car', 'microphone')"
            className="w-full px-4 py-4 pl-12 pr-12 bg-input border border-border rounded-xl text-foreground placeholder-muted-foreground focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary transition-all text-lg shadow-sm"
            disabled={isSearching}
          />
          <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-muted-foreground" />
        </div>

        <button
          type="submit"
          disabled={!searchQuery.trim() || isSearching}
          className="w-full bg-gradient-to-r from-primary to-primary/90 hover:from-primary/90 hover:to-primary disabled:bg-muted disabled:text-muted-foreground disabled:cursor-not-allowed text-primary-foreground font-semibold py-4 px-6 rounded-xl transition-all duration-300 transform hover:scale-[1.02] shadow-lg hover:shadow-xl flex items-center justify-center gap-3 text-lg"
        >
          {isSearching ? (
            <>
              <div className="animate-spin rounded-full h-6 w-6 border-2 border-primary-foreground border-t-transparent"></div>
              Streaming Search...
            </>
          ) : (
            <>
              <Zap className="w-6 h-6" />
              Fast Streaming Search
            </>
          )}
        </button>
      </form>

      {/* Progress Display */}
      {isSearching && progressUpdate && (
        <div className="bg-muted/50 rounded-lg border border-border p-4 space-y-3">
          <div className="flex items-center gap-3">
            {getStageIcon(progressUpdate.stage)}
            <div className="flex-1">
              <div className="flex items-center justify-between mb-1">
                <span className="text-sm font-medium">{getStageLabel(progressUpdate.stage)}</span>
                <span className="text-xs text-muted-foreground">
                  {progressUpdate.elapsed_time.toFixed(1)}s
                  {progressUpdate.estimated_remaining && (
                    <> • ~{progressUpdate.estimated_remaining.toFixed(1)}s remaining</>
                  )}
                </span>
              </div>
              
              {/* Progress Bar */}
              <div className="w-full bg-muted rounded-full h-2">
                <div 
                  className="bg-primary h-2 rounded-full transition-all duration-300"
                  style={{ width: `${Math.min(progressUpdate.progress_percent, 100)}%` }}
                />
              </div>
              
              <div className="flex items-center justify-between mt-1 text-xs text-muted-foreground">
                <span>
                  {progressUpdate.frames_processed}/{progressUpdate.total_frames} frames
                </span>
                <span>
                  {progressUpdate.matches_found} matches found
                </span>
              </div>
            </div>
          </div>
          
          <p className="text-sm text-muted-foreground">{progressUpdate.current_message}</p>
        </div>
      )}

      {/* Partial Results (shown during search) */}
      {isSearching && partialResults.length > 0 && (
        <div className="space-y-2">
          <h3 className="text-sm font-medium text-muted-foreground">Live Results:</h3>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2">
            {partialResults.slice(-8).map((result, index) => (
              <div
                key={index}
                className="bg-card rounded-lg border border-border p-2 hover:bg-muted/50 transition-all duration-200 cursor-pointer group animate-in fade-in-0 slide-in-from-bottom-2"
                onClick={() => onTimeJump(result.timestamp)}
              >
                <div className="aspect-video bg-muted rounded mb-2 relative overflow-hidden">
                  <img
                    src={result.frame_path}
                    alt={`Frame at ${formatTime(result.timestamp)}`}
                    className="w-full h-full object-cover"
                    onError={(e) => {
                      e.currentTarget.style.display = 'none';
                    }}
                  />
                  <div className="absolute bottom-1 right-1 bg-black/70 text-white text-xs px-1 rounded">
                    {formatTime(result.timestamp)}
                  </div>
                </div>
                <p className="text-xs text-muted-foreground truncate">
                  {(result.confidence * 100).toFixed(0)}% match
                </p>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Final Results */}
      {hasSearched && !isSearching && (
        <div className="space-y-4">
          {searchResults.length > 0 ? (
            <>
              {directAnswer && (
                <div className="mb-4 p-4 bg-muted/50 rounded-lg border border-border">
                  <div className="flex items-start gap-3">
                    <div className="w-8 h-8 bg-primary rounded-full flex items-center justify-center flex-shrink-0">
                      <CheckCircle className="w-4 h-4 text-primary-foreground" />
                    </div>
                    <div>
                      <p className="text-sm font-medium mb-1">Search Complete</p>
                      <p className="text-sm text-muted-foreground">{directAnswer}</p>
                    </div>
                  </div>
                </div>
              )}
              
              {/* Results display would go here - similar to original VisualSearch component */}
              <div className="text-center text-muted-foreground">
                <p>Found {searchResults.length} results for "{searchQuery}"</p>
                <p className="text-xs mt-1">Click on any result to jump to that moment</p>
              </div>
            </>
          ) : (
            <div className="text-center py-8">
              <div className="w-16 h-16 bg-muted rounded-full mx-auto flex items-center justify-center mb-4">
                <Search className="w-8 h-8 text-muted-foreground" />
              </div>
              <p className="text-muted-foreground">
                No matches found for "{searchQuery}". Try different keywords or check if the content exists in your video.
              </p>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default StreamingVisualSearch;
