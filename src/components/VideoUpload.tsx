import React, { useState } from 'react';
import { Play, Video, Upload, FileVideo } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { apiService } from '@/services/api';
import VideoIndexingProgress from './VideoIndexingProgress';

interface VideoUploadProps {
  onVideoUpload: (videoId: number, videoUrl: string, title: string, videoUrls?: any) => void;
}

const VideoUpload: React.FC<VideoUploadProps> = ({ onVideoUpload }) => {
  const [youtubeUrl, setYoutubeUrl] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [currentVideoId, setCurrentVideoId] = useState<number | null>(null);
  const [showIndexingProgress, setShowIndexingProgress] = useState(false);
  const [uploadMode, setUploadMode] = useState<'file' | 'youtube'>('file');
  const { toast } = useToast();

  const handleFileUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Validate file type
    if (!file.type.startsWith('video/')) {
      toast({
        title: "Invalid file type",
        description: "Please select a video file",
        variant: "destructive",
      });
      return;
    }

    // Check file size (limit to 500MB)
    if (file.size > 500 * 1024 * 1024) {
      toast({
        title: "File too large",
        description: "Please select a video file smaller than 500MB",
        variant: "destructive",
      });
      return;
    }

    setIsProcessing(true);

    try {
      const result = await apiService.uploadVideo(file);

      const getApiUrl = () => {
        if (window.location.hostname !== 'localhost' && window.location.hostname !== '127.0.0.1') {
          return 'https://videoanalysis-hquv.onrender.com';
        }
        return import.meta.env.VITE_API_URL || 'http://localhost:8002';
      };

      // Use video_urls from upload response if available, otherwise get video info
      let videoUrl: string;
      let videoUrls: any;

      if (result.video_urls?.local_url) {
        videoUrl = `${getApiUrl()}${result.video_urls.local_url}`;
        videoUrls = result.video_urls;
      } else {
        // Fallback: get video info
        const videoInfo = await apiService.getVideo(result.video_id);
        videoUrl = videoInfo.video_urls?.local_url
          ? `${getApiUrl()}${videoInfo.video_urls.local_url}`
          : URL.createObjectURL(file);
        videoUrls = videoInfo.video_urls;
      }

      onVideoUpload(result.video_id, videoUrl, result.title, videoUrls);
      setIsProcessing(false);

      toast({
        title: "Video uploaded successfully!",
        description: `"${result.title}" is ready for chat and visual search.`,
      });
    } catch (error) {
      toast({
        title: "Failed to upload video",
        description: error instanceof Error ? error.message : "Please try again",
        variant: "destructive",
      });
      setIsProcessing(false);
    }
  };

  const handleYouTubeSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!youtubeUrl.trim()) return;

    // Validate YouTube URL
    if (!isValidYouTubeUrl(youtubeUrl)) {
      toast({
        title: "Invalid YouTube URL",
        description: "Please enter a valid YouTube video URL",
        variant: "destructive",
      });
      return;
    }

    setIsProcessing(true);

    try {
      const result = await apiService.processYouTubeVideo(youtubeUrl);

      // Check if video is already completed
      if (result.status === 'completed') {
        // Video is already processed, go directly to video
        try {
          const videoInfo = await apiService.getVideo(result.video_id);
          const getApiUrl = () => {
            if (window.location.hostname !== 'localhost' && window.location.hostname !== '127.0.0.1') {
              return 'https://videoanalysis-hquv.onrender.com';
            }
            return import.meta.env.VITE_API_URL || 'http://localhost:8002';
          };
          const videoUrl = videoInfo.video_urls?.local_url
            ? `${getApiUrl()}${videoInfo.video_urls.local_url}`
            : videoInfo.video_urls?.original_url || youtubeUrl;

          onVideoUpload(result.video_id, videoUrl, videoInfo.title, videoInfo.video_urls);
          setIsProcessing(false);
          setYoutubeUrl('');

          toast({
            title: "Video ready!",
            description: `"${result.title}" is already processed and ready for search.`,
          });
          return;
        } catch (error) {
          console.error('Error fetching completed video info:', error);
        }
      }

      // Video is being processed, show indexing progress
      setCurrentVideoId(result.video_id);
      setShowIndexingProgress(true);
      setIsProcessing(false);

      toast({
        title: "YouTube video processing started!",
        description: `Video "${result.title}" is being indexed for fast search. You can start searching in ~6 seconds.`,
      });
    } catch (error) {
      toast({
        title: "Failed to process YouTube video",
        description: error instanceof Error ? error.message : "Please check the URL and try again",
        variant: "destructive",
      });
      setIsProcessing(false);
    }
  };

  const isValidYouTubeUrl = (url: string): boolean => {
    const regex = /^(https?:\/\/)?(www\.)?(youtube\.com\/(watch\?v=|embed\/|v\/)|youtu\.be\/)[\w-]{11}(&.*)?$/;
    return regex.test(url);
  };

  const extractYouTubeVideoId = (url: string): string | null => {
    const regex = /(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/;
    const match = url.match(regex);
    return match ? match[1] : null;
  };

  const handleIndexingComplete = async () => {
    if (currentVideoId) {
      try {
        // Fetch video information to get the proper URLs
        const videoInfo = await apiService.getVideo(currentVideoId);

        // Determine which URL to use - prefer local if available, fallback to YouTube
        const getApiUrl = () => {
          if (window.location.hostname !== 'localhost' && window.location.hostname !== '127.0.0.1') {
            return 'https://videoanalysis-hquv.onrender.com';
          }
          return import.meta.env.VITE_API_URL || 'http://localhost:8002';
        };
        const videoUrl = videoInfo.video_urls?.local_url
          ? `${getApiUrl()}${videoInfo.video_urls.local_url}`
          : videoInfo.video_urls?.original_url || youtubeUrl;

        onVideoUpload(currentVideoId, videoUrl, videoInfo.title, videoInfo.video_urls);

        toast({
          title: "Video ready for search!",
          description: "Background indexing completed. You can now search with sub-second response times.",
        });
      } catch (error) {
        console.error('Error fetching video info:', error);
        // Fallback to YouTube URL
        const videoTitle = `YouTube Video ${currentVideoId}`;
        onVideoUpload(currentVideoId, youtubeUrl, videoTitle, null);

        toast({
          title: "Video ready for search!",
          description: "Using YouTube player. Local video may not be available.",
        });
      }
    }
    setShowIndexingProgress(false);
    setCurrentVideoId(null);
    setYoutubeUrl('');
  };

  if (showIndexingProgress && currentVideoId) {
    return (
      <div className="max-w-2xl mx-auto space-y-6">
        <VideoIndexingProgress
          videoId={currentVideoId}
          onIndexingComplete={handleIndexingComplete}
        />

        {/* Option to continue without waiting */}
        <div className="text-center">
          <button
            onClick={handleIndexingComplete}
            className="text-sm text-blue-600 hover:text-blue-800 underline"
          >
            Continue to video (indexing will complete in background)
          </button>
        </div>
      </div>
    );
  }

  if (isProcessing) {
    return (
      <div className="max-w-2xl mx-auto">
        <div className="bg-card border border-border rounded-2xl p-12 text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-4 border-foreground border-t-transparent mx-auto mb-6"></div>
          <h2 className="text-2xl font-bold text-foreground mb-2">Processing YouTube Video...</h2>
          <p className="text-muted-foreground">
            We're extracting the transcript and preparing it for AI analysis
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-2xl mx-auto">
      {/* Upload Mode Toggle */}
      <div className="flex justify-center mb-6">
        <div className="bg-muted p-1 rounded-lg flex">
          <button
            onClick={() => setUploadMode('file')}
            className={`px-4 py-2 rounded-md flex items-center gap-2 transition-all ${
              uploadMode === 'file'
                ? 'bg-background text-foreground shadow-sm'
                : 'text-muted-foreground hover:text-foreground'
            }`}
          >
            <FileVideo className="w-4 h-4" />
            Upload Video File
          </button>
          <button
            onClick={() => setUploadMode('youtube')}
            className={`px-4 py-2 rounded-md flex items-center gap-2 transition-all ${
              uploadMode === 'youtube'
                ? 'bg-background text-foreground shadow-sm'
                : 'text-muted-foreground hover:text-foreground'
            }`}
          >
            <Video className="w-4 h-4" />
            YouTube URL
          </button>
        </div>
      </div>

      {/* Upload Content */}
      <div className="bg-card border border-border rounded-2xl p-8">
        {uploadMode === 'file' ? (
          // File Upload Section
          <>
            <div className="flex items-center gap-3 mb-6 justify-center">
              <FileVideo className="w-10 h-10 text-blue-600" />
              <h3 className="text-2xl font-bold text-foreground">
                Upload Video File
              </h3>
            </div>

            <p className="text-center text-muted-foreground mb-8">
              Upload your video file for AI-powered chat and visual search. Works 100% reliably!
            </p>

            <div className="space-y-6">
              <div className="border-2 border-dashed border-border rounded-lg p-8 text-center hover:border-blue-500 transition-colors">
                <input
                  type="file"
                  accept="video/*"
                  onChange={handleFileUpload}
                  disabled={isProcessing}
                  className="hidden"
                  id="video-upload"
                />
                <label
                  htmlFor="video-upload"
                  className="cursor-pointer flex flex-col items-center gap-4"
                >
                  <Upload className="w-12 h-12 text-muted-foreground" />
                  <div>
                    <p className="text-lg font-medium text-foreground">
                      Click to upload or drag and drop
                    </p>
                    <p className="text-sm text-muted-foreground">
                      MP4, MOV, AVI, WebM (max 500MB)
                    </p>
                  </div>
                </label>
              </div>

              {isProcessing && (
                <div className="text-center">
                  <div className="inline-flex items-center gap-2 text-blue-600">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                    Processing video...
                  </div>
                </div>
              )}
            </div>
          </>
        ) : (
          // YouTube URL Section
          <>
            <div className="flex items-center gap-3 mb-6 justify-center">
              <Video className="w-10 h-10 text-red-600" />
              <h3 className="text-2xl font-bold text-foreground">
                Add YouTube Video
              </h3>
            </div>

            <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4 mb-6">
              <p className="text-sm text-yellow-800 dark:text-yellow-200">
                ⚠️ <strong>Note:</strong> YouTube videos may not work on cloud platforms due to blocking.
                File upload is recommended for reliable functionality.
              </p>
            </div>

            <p className="text-center text-muted-foreground mb-8">
              Enter a YouTube video URL to analyze and chat with the video content using AI
            </p>

        <form onSubmit={handleYouTubeSubmit} className="space-y-6">
          <div>
            <label htmlFor="youtube-url" className="block text-sm font-medium text-foreground mb-2">
              YouTube Video URL
            </label>
            <input
              id="youtube-url"
              type="url"
              value={youtubeUrl}
              onChange={(e) => setYoutubeUrl(e.target.value)}
              placeholder="https://www.youtube.com/watch?v=dQw4w9WgXcQ"
              className="w-full px-4 py-3 bg-input border border-border rounded-lg text-foreground placeholder-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent transition-all"
            />
          </div>

          <button
            type="submit"
            disabled={!youtubeUrl.trim() || isProcessing}
            className="w-full bg-red-600 hover:bg-red-700 disabled:bg-muted disabled:text-muted-foreground disabled:cursor-not-allowed text-white font-semibold py-4 px-6 rounded-lg transition-all duration-300 transform hover:scale-105 shadow-lg flex items-center justify-center gap-3"
          >
            <Play className="w-5 h-5" />
            Load & Analyze YouTube Video
          </button>
        </form>


        {/* Example URLs */}
        <div className="mt-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
          <p className="text-sm font-medium text-blue-800 dark:text-blue-200 mb-2">Try with these example videos:</p>
          <div className="space-y-2">
            {[
              { url: "https://www.youtube.com/watch?v=dQw4w9WgXcQ", title: "Classic Music Video" },
              { url: "https://www.youtube.com/watch?v=jNQXAC9IVRw", title: "Educational Content" },
            ].map((example, index) => (
              <button
                key={index}
                onClick={() => setYoutubeUrl(example.url)}
                className="block w-full text-left text-xs text-blue-700 dark:text-blue-300 hover:text-blue-900 dark:hover:text-blue-100 transition-colors p-2 rounded bg-white/50 dark:bg-black/20 hover:bg-white dark:hover:bg-black/40"
              >
                <span className="font-medium">{example.title}</span>
                <br />
                <span className="text-blue-600 dark:text-blue-400">{example.url}</span>
              </button>
            ))}
          </div>
        </div>
          </>
        )}
      </div>

      {/* Indexing Progress */}
      {showIndexingProgress && currentVideoId && (
        <VideoIndexingProgress
          videoId={currentVideoId}
          onComplete={handleIndexingComplete}
        />
      )}
    </div>
  );
};

export default VideoUpload;