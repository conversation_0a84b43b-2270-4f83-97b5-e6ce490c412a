
import React, { useState, useEffect } from 'react';
import { Clock, Play, Hash, RefreshCw } from 'lucide-react';
import { apiService, VideoSection } from '@/services/api';
import { useToast } from '@/hooks/use-toast';

interface VideoSectionsProps {
  videoId: number;
  videoUrl: string;
  videoDuration?: number; // Add video duration prop
  onTimeJump: (time: number) => void;
}

const VideoSections: React.FC<VideoSectionsProps> = ({ videoId, videoUrl, videoDuration, onTimeJump }) => {
  const [sections, setSections] = useState<VideoSection[]>([]);
  const [loading, setLoading] = useState(true);
  const [generating, setGenerating] = useState(false);
  const { toast } = useToast();

  // Calculate total video duration for timeline calculations
  const totalDuration = videoDuration || 420; // Default to 7 minutes if not provided

  useEffect(() => {
    const fetchSections = async () => {
      try {
        setLoading(true);
        console.log('Fetching sections for video:', videoId);
        const response = await apiService.getVideoSections(videoId);
        console.log('Sections response:', response);

        if (response.sections && response.sections.length > 0) {
          setSections(response.sections);
          console.log('Loaded', response.sections.length, 'sections');
        } else {
          console.log('No sections found for video', videoId);
          setSections([]);
        }
      } catch (error) {
        console.error('Error fetching video sections:', error);
        toast({
          title: "Error loading sections",
          description: error instanceof Error ? error.message : "Failed to load video sections",
          variant: "destructive",
        });
        setSections([]);
      } finally {
        setLoading(false);
      }
    };

    fetchSections();
  }, [videoId, toast]);

  const generateSections = async () => {
    try {
      setGenerating(true);
      console.log('Generating sections for video:', videoId);

      const response = await fetch(`http://localhost:8002/api/v1/video/${videoId}/sections/generate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Failed to generate sections');
      }

      const result = await response.json();
      console.log('Generated sections:', result);

      if (result.sections && result.sections.length > 0) {
        setSections(result.sections);
        toast({
          title: "Success",
          description: `Generated ${result.sections.length} sections successfully`,
        });
      } else {
        throw new Error('No sections were generated');
      }
    } catch (error) {
      console.error('Error generating sections:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to generate sections",
        variant: "destructive",
      });
    } finally {
      setGenerating(false);
    }
  };

  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const parseTimeToSeconds = (timeStr: string): number => {
    const parts = timeStr.split(':');
    if (parts.length === 2) {
      return parseInt(parts[0]) * 60 + parseInt(parts[1]);
    }
    return 0;
  };

  const getDuration = (startTime: number, endTime: number): string => {
    const duration = endTime - startTime;
    return `${Math.floor(duration / 60)}:${(duration % 60).toString().padStart(2, '0')}`;
  };

  if (loading) {
    return (
      <div className="h-full flex flex-col bg-black">
        <div className="text-center mb-4 flex-shrink-0">
          <h3 className="text-lg font-semibold text-white mb-2">Video Sections</h3>
          <p className="text-sm text-gray-300">Loading sections...</p>
        </div>
        <div className="flex-1 animate-pulse space-y-3">
          {[1, 2, 3].map((i) => (
            <div key={i} className="bg-gray-900/80 rounded-lg p-4 h-24 border border-gray-700"></div>
          ))}
        </div>
      </div>
    );
  }

  // Show empty state if no sections
  if (!loading && sections.length === 0) {
    return (
      <div className="h-full flex flex-col bg-black">
        <div className="text-center mb-4 flex-shrink-0">
          <h3 className="text-lg font-semibold text-white mb-2">Video Sections</h3>
          <p className="text-sm text-gray-300">
            AI-generated breakdown with clickable timestamps
          </p>
        </div>

        <div className="flex-1 flex flex-col items-center justify-center space-y-4">
          <div className="text-center">
            <Clock className="w-12 h-12 text-gray-500 mx-auto mb-3" />
            <h4 className="text-lg font-medium text-white mb-2">No Sections Available</h4>
            <p className="text-sm text-gray-400 mb-4 max-w-sm">
              This video doesn't have sections yet. Generate them using AI to get a detailed breakdown with timestamps.
            </p>
            <button
              onClick={generateSections}
              disabled={generating}
              className="bg-white hover:bg-gray-200 text-black px-6 py-2 rounded-lg font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {generating ? 'Generating...' : 'Generate Sections'}
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col bg-black">
      {/* Header */}
      <div className="text-center mb-4 flex-shrink-0">
        <div className="flex items-center justify-between mb-2">
          <div></div>
          <h3 className="text-lg font-semibold text-white">Video Sections</h3>
          <button
            onClick={() => window.location.reload()}
            className="text-gray-400 hover:text-white transition-colors p-1"
            title="Refresh sections"
          >
            <RefreshCw className="w-4 h-4" />
          </button>
        </div>
        <p className="text-sm text-gray-300">
          AI-generated breakdown with clickable timestamps
        </p>
      </div>

      {/* Sections List - Scrollable */}
      <div className="flex-1 overflow-y-auto space-y-3 pr-2 scrollbar-thin scrollbar-thumb-gray-500/50 scrollbar-track-transparent">
        {sections.map((section, index) => (
          <div
            key={index}
            className="bg-gray-900/80 rounded-lg border border-gray-700 p-4 hover:bg-gray-800/80 transition-all duration-300 cursor-pointer group"
            onClick={() => onTimeJump(parseTimeToSeconds(section.start_time))}
          >
            {/* Section Header */}
            <div className="flex items-start justify-between mb-3">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-white rounded-full flex items-center justify-center text-black font-semibold text-sm">
                  {index + 1}
                </div>
                <div>
                  <h4 className="font-medium text-white group-hover:text-gray-300 transition-colors">
                    {section.title}
                  </h4>
                  <div className="flex items-center gap-4 text-xs text-gray-400 mt-1">
                    <span className="flex items-center gap-1">
                      <Clock className="w-3 h-3" />
                      {section.start_time} - {section.end_time}
                    </span>
                    <span>Duration: {getDuration(parseTimeToSeconds(section.start_time), parseTimeToSeconds(section.end_time))}</span>
                  </div>
                </div>
              </div>

              <button
                onClick={(e) => {
                  e.stopPropagation();
                  onTimeJump(parseTimeToSeconds(section.start_time));
                }}
                className="opacity-0 group-hover:opacity-100 transition-opacity bg-white hover:bg-gray-200 text-black p-2 rounded-full transform hover:scale-110 transition-transform"
              >
                <Play className="w-4 h-4" />
              </button>
            </div>

            {/* Section Description */}
            <p className="text-sm text-gray-300 mb-3 leading-relaxed">
              {section.description}
            </p>

            {/* Key Topics */}
            <div className="flex flex-wrap gap-2">
              {section.key_topics.map((topic, topicIndex) => (
                <span
                  key={topicIndex}
                  className="inline-flex items-center gap-1 px-2 py-1 bg-gray-800 rounded text-xs text-gray-300 border border-gray-600"
                >
                  <Hash className="w-3 h-3" />
                  {topic}
                </span>
              ))}
            </div>

            {/* Progress Bar */}
            <div className="mt-3 h-1 bg-gray-700 rounded-full overflow-hidden">
              <div
                className="h-full bg-white rounded-full"
                style={{
                  width: `${((parseTimeToSeconds(section.end_time) - parseTimeToSeconds(section.start_time)) / totalDuration) * 100}%`
                }}
              />
            </div>
          </div>
        ))}
      </div>

      {/* Timeline Overview */}
      <div className="mt-4 p-4 bg-gray-900/80 rounded-lg border border-gray-700 flex-shrink-0">
        <h4 className="text-sm font-medium text-white mb-3">Timeline Overview</h4>
        <div className="relative h-2 bg-gray-700 rounded-full">
          {sections.map((section, index) => (
            <div
              key={index}
              className="absolute top-0 h-full bg-white rounded-full cursor-pointer hover:opacity-80 transition-opacity"
              style={{
                left: `${(parseTimeToSeconds(section.start_time) / totalDuration) * 100}%`,
                width: `${((parseTimeToSeconds(section.end_time) - parseTimeToSeconds(section.start_time)) / totalDuration) * 100}%`,
              }}
              onClick={() => onTimeJump(parseTimeToSeconds(section.start_time))}
              title={`${section.title} (${section.start_time})`}
            />
          ))}
        </div>
        <div className="flex justify-between text-xs text-gray-400 mt-2">
          <span>0:00</span>
          <span>{Math.floor(totalDuration / 60)}:{(totalDuration % 60).toString().padStart(2, '0')}</span>
        </div>
      </div>
    </div>
  );
};

export default VideoSections;
