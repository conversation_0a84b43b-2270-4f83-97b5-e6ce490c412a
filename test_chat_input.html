<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Chat Input - Space Handling</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background: #1a1a1a;
            color: white;
        }
        .test-container {
            background: #2a2a2a;
            border: 1px solid #444;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .input-test {
            width: 100%;
            padding: 12px;
            border: 1px solid #555;
            border-radius: 8px;
            background: #333;
            color: white;
            font-size: 16px;
            margin: 10px 0;
            white-space: pre-wrap;
            word-wrap: break-word;
        }
        .textarea-test {
            width: 100%;
            padding: 12px;
            border: 1px solid #555;
            border-radius: 8px;
            background: #333;
            color: white;
            font-size: 16px;
            margin: 10px 0;
            resize: vertical;
            min-height: 48px;
            white-space: pre-wrap;
            word-wrap: break-word;
            font-family: inherit;
        }
        .output {
            background: #444;
            padding: 12px;
            border-radius: 8px;
            margin: 10px 0;
            white-space: pre-wrap;
            word-wrap: break-word;
            border: 1px solid #666;
        }
        .button {
            background: white;
            color: black;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
        }
        .button:hover {
            background: #ddd;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 6px;
            background: #333;
        }
        .success { background: #1a4a1a; color: #51cf66; }
        .error { background: #4a1a1a; color: #ff6b6b; }
    </style>
</head>
<body>
    <h1>🧪 Chat Input Space Handling Test</h1>
    
    <div class="test-container">
        <h3>Test 1: Regular Input Field</h3>
        <input 
            type="text" 
            class="input-test" 
            placeholder="Type with spaces here..."
            id="input1"
            oninput="updateOutput('input1', 'output1')"
        >
        <div class="output" id="output1">Output will appear here...</div>
    </div>

    <div class="test-container">
        <h3>Test 2: Textarea (Recommended)</h3>
        <textarea 
            class="textarea-test" 
            placeholder="Type with spaces here... (Press Enter for new line)"
            id="textarea1"
            oninput="updateOutput('textarea1', 'output2')"
        ></textarea>
        <div class="output" id="output2">Output will appear here...</div>
    </div>

    <div class="test-container">
        <h3>Test 3: Chat Simulation</h3>
        <textarea 
            class="textarea-test" 
            placeholder="Ask anything about this video... (Press Enter to send)"
            id="chatInput"
            onkeydown="handleChatKeydown(event)"
        ></textarea>
        <button class="button" onclick="sendChatMessage()">Send Message</button>
        <div id="chatMessages"></div>
    </div>

    <div class="test-container">
        <h3>Test Results</h3>
        <button class="button" onclick="runSpaceTest()">Run Space Test</button>
        <button class="button" onclick="runMultiWordTest()">Run Multi-Word Test</button>
        <button class="button" onclick="runSpecialCharsTest()">Run Special Characters Test</button>
        <div id="testResults" class="status">Click a test button to see results...</div>
    </div>

    <script>
        function updateOutput(inputId, outputId) {
            const input = document.getElementById(inputId);
            const output = document.getElementById(outputId);
            const value = input.value;
            
            output.innerHTML = `
                <strong>Raw Value:</strong> "${value}"<br>
                <strong>Length:</strong> ${value.length}<br>
                <strong>Spaces Count:</strong> ${(value.match(/ /g) || []).length}<br>
                <strong>JSON:</strong> ${JSON.stringify(value)}
            `;
        }

        function handleChatKeydown(event) {
            if (event.key === 'Enter' && !event.shiftKey) {
                event.preventDefault();
                sendChatMessage();
            }
        }

        function sendChatMessage() {
            const input = document.getElementById('chatInput');
            const messages = document.getElementById('chatMessages');
            const message = input.value.trim();
            
            if (!message) return;
            
            const messageDiv = document.createElement('div');
            messageDiv.className = 'output';
            messageDiv.innerHTML = `
                <strong>Message:</strong> "${message}"<br>
                <strong>Length:</strong> ${message.length}<br>
                <strong>Spaces:</strong> ${(message.match(/ /g) || []).length}<br>
                <strong>Time:</strong> ${new Date().toLocaleTimeString()}
            `;
            
            messages.appendChild(messageDiv);
            input.value = '';
        }

        function runSpaceTest() {
            const testString = "hello world test";
            const results = document.getElementById('testResults');
            
            results.className = 'status success';
            results.innerHTML = `
                <strong>✅ Space Test Results:</strong><br>
                Input: "${testString}"<br>
                Length: ${testString.length}<br>
                Spaces: ${(testString.match(/ /g) || []).length}<br>
                JSON: ${JSON.stringify(testString)}<br>
                <em>Spaces should be preserved correctly!</em>
            `;
        }

        function runMultiWordTest() {
            const testString = "how are you doing today";
            const results = document.getElementById('testResults');
            
            results.className = 'status success';
            results.innerHTML = `
                <strong>✅ Multi-Word Test Results:</strong><br>
                Input: "${testString}"<br>
                Words: ${testString.split(' ').length}<br>
                Spaces: ${(testString.match(/ /g) || []).length}<br>
                Split: [${testString.split(' ').map(w => `"${w}"`).join(', ')}]<br>
                <em>All words should be separated properly!</em>
            `;
        }

        function runSpecialCharsTest() {
            const testString = "hello   world\ttab\nnewline";
            const results = document.getElementById('testResults');
            
            results.className = 'status success';
            results.innerHTML = `
                <strong>✅ Special Characters Test:</strong><br>
                Input: "${testString}"<br>
                Length: ${testString.length}<br>
                JSON: ${JSON.stringify(testString)}<br>
                <em>Special characters should be handled correctly!</em>
            `;
        }

        // Auto-test on page load
        window.onload = function() {
            console.log('Chat input test page loaded');
            runSpaceTest();
        };
    </script>
</body>
</html>
